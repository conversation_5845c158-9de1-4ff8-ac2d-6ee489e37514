# إعدادات نظام أرشفة التقارير الطبية
# Medical Reports Archive System Configuration

import os

# إعدادات قاعدة البيانات
DATABASE_URL = 'sqlite:///medical_reports.db'

# إعدادات الأمان
SECRET_KEY = 'medical-reports-archive-secret-key-2024'

# إعدادات الملفات
UPLOAD_FOLDER = 'archives'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# إعدادات المسح الضوئي
DEFAULT_SCAN_QUALITY = '300dpi'
DEFAULT_FILE_TYPE = 'PDF'
SUPPORTED_FORMATS = ['PDF', 'JPG', 'PNG']
QUALITY_OPTIONS = ['150dpi', '300dpi', '600dpi']

# إعدادات الخادم
HOST = '0.0.0.0'
PORT = 5000
DEBUG = True

# إعدادات التطبيق
APP_NAME = 'نظام أرشفة التقارير الطبية'
APP_VERSION = '1.0.0'
APP_AUTHOR = 'فريق التطوير'

# إعدادات اللغة والمنطقة
LANGUAGE = 'ar'
TIMEZONE = 'Asia/Riyadh'
DATE_FORMAT = '%Y-%m-%d'
DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'

# إعدادات النسخ الاحتياطي
BACKUP_FOLDER = 'backups'
AUTO_BACKUP = True
BACKUP_INTERVAL_HOURS = 24

# إعدادات التصدير
EXPORT_FOLDER = 'exports'
EXCEL_TEMPLATE = 'templates/excel_template.xlsx'
PDF_TEMPLATE = 'templates/pdf_template.html'

# إعدادات الأمان المتقدمة
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
SCAN_TIMEOUT = 30  # ثانية

# إعدادات قاعدة البيانات المتقدمة
SQLALCHEMY_DATABASE_URI = DATABASE_URL
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_ECHO = False  # تعيين إلى True لعرض استعلامات SQL

# إعدادات الجلسة
PERMANENT_SESSION_LIFETIME = 3600  # ساعة واحدة

# إعدادات التسجيل
LOG_LEVEL = 'INFO'
LOG_FILE = 'logs/app.log'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# إعدادات الأداء
CACHE_TYPE = 'simple'
CACHE_DEFAULT_TIMEOUT = 300

# إعدادات البريد الإلكتروني (للإشعارات المستقبلية)
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = ''
MAIL_PASSWORD = ''

# إعدادات التطوير
TESTING = False
WTF_CSRF_ENABLED = True

# دالة للحصول على الإعدادات
def get_config():
    """إرجاع جميع الإعدادات كقاموس"""
    return {
        'DATABASE_URL': DATABASE_URL,
        'SECRET_KEY': SECRET_KEY,
        'UPLOAD_FOLDER': UPLOAD_FOLDER,
        'MAX_CONTENT_LENGTH': MAX_CONTENT_LENGTH,
        'DEFAULT_SCAN_QUALITY': DEFAULT_SCAN_QUALITY,
        'DEFAULT_FILE_TYPE': DEFAULT_FILE_TYPE,
        'SUPPORTED_FORMATS': SUPPORTED_FORMATS,
        'HOST': HOST,
        'PORT': PORT,
        'DEBUG': DEBUG,
        'APP_NAME': APP_NAME,
        'APP_VERSION': APP_VERSION,
        'LANGUAGE': LANGUAGE,
        'TIMEZONE': TIMEZONE
    }

# دالة لتحديث الإعدادات
def update_config(key, value):
    """تحديث إعداد معين"""
    globals()[key] = value

# دالة للتحقق من صحة الإعدادات
def validate_config():
    """التحقق من صحة الإعدادات"""
    errors = []
    
    # التحقق من وجود المجلدات المطلوبة
    required_folders = [UPLOAD_FOLDER, BACKUP_FOLDER, 'logs']
    for folder in required_folders:
        if not os.path.exists(folder):
            try:
                os.makedirs(folder)
            except Exception as e:
                errors.append(f"لا يمكن إنشاء المجلد {folder}: {e}")
    
    # التحقق من صحة تنسيقات الملفات
    if DEFAULT_FILE_TYPE not in SUPPORTED_FORMATS:
        errors.append(f"تنسيق الملف الافتراضي {DEFAULT_FILE_TYPE} غير مدعوم")
    
    # التحقق من صحة جودة المسح
    if DEFAULT_SCAN_QUALITY not in QUALITY_OPTIONS:
        errors.append(f"جودة المسح الافتراضية {DEFAULT_SCAN_QUALITY} غير صحيحة")
    
    return errors
