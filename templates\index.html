{% extends "base.html" %}

{% block title %}الرئيسية - نظام أرشفة التقارير الطبية{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h1 class="display-4 text-primary mb-3">
                        <i class="bi bi-hospital"></i>
                        نظام أرشفة التقارير الطبية
                    </h1>
                    <p class="lead text-muted">
                        نظام شامل لأرشفة وإدارة التقارير الطبية مع إمكانيات المسح الضوئي والإحصائيات المتقدمة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">إجمالي التقارير</h6>
                        <div class="stats-number" id="totalReports">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">تقارير هذا الشهر</h6>
                        <div class="stats-number" id="monthlyReports">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-calendar-month" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">ذوي الإعاقة</h6>
                        <div class="stats-number" id="disabilityReports">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-person-wheelchair" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">الرعاية الاجتماعية</h6>
                        <div class="stats-number" id="socialCareReports">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-heart-pulse" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ url_for('scan_page') }}" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                                <i class="bi bi-scanner" style="font-size: 2rem;"></i>
                                <span class="mt-2">مسح ضوئي جديد</span>
                            </a>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ url_for('reports_page') }}" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                                <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
                                <span class="mt-2">عرض التقارير</span>
                            </a>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ url_for('statistics_page') }}" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                                <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                                <span class="mt-2">الإحصائيات</span>
                            </a>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ url_for('export_excel') }}" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                                <i class="bi bi-file-earmark-excel" style="font-size: 2rem;"></i>
                                <span class="mt-2">تصدير Excel</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i>
                        آخر التقارير
                    </h5>
                    <a href="{{ url_for('reports_page') }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div id="recentReports">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart"></i>
                        توزيع أنواع التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="reportTypesChart" width="300" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- System Info -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>الميزات الرئيسية:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> مسح ضوئي متقدم</li>
                                <li><i class="bi bi-check-circle text-success"></i> أرشفة منظمة</li>
                                <li><i class="bi bi-check-circle text-success"></i> إحصائيات تفاعلية</li>
                                <li><i class="bi bi-check-circle text-success"></i> تصدير متعدد التنسيقات</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>التنسيقات المدعومة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-file-earmark-pdf text-danger"></i> PDF</li>
                                <li><i class="bi bi-file-earmark-image text-primary"></i> JPG</li>
                                <li><i class="bi bi-file-earmark-image text-info"></i> PNG</li>
                                <li><i class="bi bi-file-earmark-excel text-success"></i> Excel</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>جودة المسح:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-speedometer text-warning"></i> 150 DPI</li>
                                <li><i class="bi bi-speedometer text-info"></i> 300 DPI</li>
                                <li><i class="bi bi-speedometer text-success"></i> 600 DPI</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الإحصائيات السريعة
    loadQuickStats();
    
    // تحميل آخر التقارير
    loadRecentReports();
    
    // تحميل رسم توزيع أنواع التقارير
    loadReportTypesChart();
});

function loadQuickStats() {
    fetch('/api/statistics/yearly')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.data;
                document.getElementById('totalReports').textContent = formatNumber(stats.total_reports);
                
                // حساب تقارير الشهر الحالي
                const currentMonth = new Date().getMonth() + 1;
                const monthlyData = stats.monthly_data.find(m => m.month === currentMonth);
                document.getElementById('monthlyReports').textContent = formatNumber(monthlyData ? monthlyData.count : 0);
                
                // حساب تقارير ذوي الإعاقة والرعاية الاجتماعية
                let disabilityCount = 0;
                let socialCareCount = 0;
                
                stats.report_types.forEach(rt => {
                    if (rt.type === 'ذوي الإعاقة') {
                        disabilityCount = rt.count;
                    } else if (rt.type === 'الرعاية الاجتماعية') {
                        socialCareCount = rt.count;
                    }
                });
                
                document.getElementById('disabilityReports').textContent = formatNumber(disabilityCount);
                document.getElementById('socialCareReports').textContent = formatNumber(socialCareCount);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
        });
}

function loadRecentReports() {
    // محاكاة تحميل آخر التقارير
    setTimeout(() => {
        const recentReportsHtml = `
            <div class="list-group list-group-flush">
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">لا توجد تقارير حديثة</h6>
                        <p class="mb-1 text-muted">ابدأ بإضافة تقرير جديد</p>
                    </div>
                    <small class="text-muted">الآن</small>
                </div>
            </div>
        `;
        document.getElementById('recentReports').innerHTML = recentReportsHtml;
    }, 1000);
}

function loadReportTypesChart() {
    const ctx = document.getElementById('reportTypesChart').getContext('2d');
    
    // بيانات وهمية للرسم البياني
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['ذوي الإعاقة', 'الرعاية الاجتماعية'],
            datasets: [{
                data: [0, 0],
                backgroundColor: [
                    '#667eea',
                    '#764ba2'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>
{% endblock %}
