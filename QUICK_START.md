# دليل البدء السريع - نظام أرشفة التقارير الطبية

## 🚀 التشغيل السريع

### Windows
```bash
# تشغيل الملف التلقائي
run.bat

# أو تشغيل يدوي
python app.py
```

### Linux/Mac
```bash
# تشغيل الملف التلقائي
./run.sh

# أو تشغيل يدوي
python3 app.py
```

### فتح التطبيق
افتح المتصفح وانتقل إلى: **http://localhost:5000**

---

## 📋 الاستخدام الأساسي

### 1. المسح الضوئي الأول
1. اضغط على "مسح ضوئي" في القائمة العلوية
2. أدخل اسم المريض
3. اختر نوع التقرير (ذوي الإعاقة / الرعاية الاجتماعية)
4. اختر المرض من القائمة
5. اضغط "بدء المسح الضوئي"

### 2. عرض التقارير
1. اضغط على "التقارير" في القائمة
2. استخدم فلاتر البحث للعثور على تقارير محددة
3. اضغط على أيقونة العين لعرض التفاصيل

### 3. الإحصائيات
1. اضغط على "الإحصائيات" في القائمة
2. اختر الفترة الزمنية (شهرية/سنوية)
3. اعرض الرسوم البيانية والتقارير

---

## ⚙️ الإعدادات الأساسية

### إضافة مرض جديد
1. في صفحة المسح الضوئي، اضغط على "+" بجانب قائمة الأمراض
2. أدخل اسم المرض واختر الفئة
3. اضغط "إضافة"

### تغيير مجلد الحفظ
1. اذهب إلى "الإعدادات"
2. في قسم "الإعدادات العامة"
3. اضغط "تصفح" بجانب مجلد الحفظ الافتراضي

---

## 📤 التصدير

### تصدير إلى Excel
- من صفحة التقارير: اضغط "تصدير Excel"
- من الصفحة الرئيسية: اضغط على بطاقة "تصدير Excel"

### تصدير إلى PDF
- من صفحة الإحصائيات: اضغط "تصدير PDF"

---

## 🔧 حل المشاكل الشائعة

### المسح الضوئي لا يعمل
- تأكد من توصيل الماسح الضوئي
- في Windows: تأكد من تثبيت تعريفات الماسح
- في Linux: تأكد من تثبيت SANE drivers

### لا تظهر الرسوم البيانية
- تأكد من وجود بيانات في النظام
- أضف بعض التقارير أولاً

### خطأ في قاعدة البيانات
- اذهب إلى الإعدادات
- اضغط "إعادة تعيين قاعدة البيانات"
- ⚠️ تحذير: هذا سيحذف جميع البيانات

---

## 📁 هيكل الملفات

```
medical-reports-archive/
├── app.py              # التطبيق الرئيسي
├── run.bat            # تشغيل Windows
├── run.sh             # تشغيل Linux/Mac
├── archives/          # ملفات التقارير المحفوظة
├── logs/              # ملفات السجلات
├── medical_reports.db # قاعدة البيانات
└── templates/         # قوالب الواجهة
```

---

## 🆘 الحصول على المساعدة

### مشاكل تقنية
1. تحقق من ملف `logs/app.log` للأخطاء
2. شغل `python test_system.py` للتحقق من النظام
3. راجع ملف `README.md` للتفاصيل الكاملة

### اتصل بالدعم
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX

---

## 🔒 النسخ الاحتياطي

### نسخ احتياطي يدوي
1. اذهب إلى الإعدادات
2. اضغط "نسخ احتياطي لقاعدة البيانات"
3. احفظ ملف `medical_reports.db` في مكان آمن

### نسخ احتياطي للملفات
- انسخ مجلد `archives/` بالكامل
- احفظه في مكان آمن خارج الجهاز

---

## 📈 نصائح للاستخدام الأمثل

### تنظيم البيانات
- استخدم أسماء واضحة للمرضى
- أضف أرقام تعريفية للمرضى
- استخدم الملاحظات لمعلومات إضافية

### الأداء
- نظف السجلات القديمة دورياً
- احذف الملفات غير المستخدمة
- أنشئ نسخ احتياطية منتظمة

### الأمان
- غير كلمة المرور الافتراضية في الإنتاج
- استخدم HTTPS في البيئات الحقيقية
- قيد الوصول للشبكة المحلية فقط

---

**ملاحظة**: هذا دليل مبسط للبدء السريع. للتفاصيل الكاملة، راجع ملف `README.md`.
