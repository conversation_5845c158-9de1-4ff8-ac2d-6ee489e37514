import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import func, extract
from database import db, MedicalReport, Disease
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from io import BytesIO
import base64
import os
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class ReportGenerator:
    """مولد التقارير والإحصائيات"""
    
    def __init__(self):
        self.setup_matplotlib()
        self.setup_reportlab()
    
    def setup_matplotlib(self):
        """إعداد matplotlib للنصوص العربية"""
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def setup_reportlab(self):
        """إعداد ReportLab للنصوص العربية"""
        try:
            # محاولة تسجيل خط عربي
            pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
        except:
            # استخدام الخط الافتراضي إذا لم يتم العثور على خط عربي
            pass
    
    def get_monthly_statistics(self, year=None, month=None):
        """إحصائيات شهرية"""
        if not year:
            year = datetime.now().year
        if not month:
            month = datetime.now().month
        
        # إحصائيات عامة
        total_reports = MedicalReport.query.filter(
            extract('year', MedicalReport.created_at) == year,
            extract('month', MedicalReport.created_at) == month
        ).count()
        
        # إحصائيات حسب نوع التقرير
        report_types = db.session.query(
            MedicalReport.report_type,
            func.count(MedicalReport.id).label('count')
        ).filter(
            extract('year', MedicalReport.created_at) == year,
            extract('month', MedicalReport.created_at) == month
        ).group_by(MedicalReport.report_type).all()
        
        # إحصائيات حسب المرض
        diseases = db.session.query(
            Disease.name,
            func.count(MedicalReport.id).label('count')
        ).join(MedicalReport).filter(
            extract('year', MedicalReport.created_at) == year,
            extract('month', MedicalReport.created_at) == month
        ).group_by(Disease.name).order_by(func.count(MedicalReport.id).desc()).limit(10).all()
        
        return {
            'total_reports': total_reports,
            'report_types': [{'type': rt[0], 'count': rt[1]} for rt in report_types],
            'top_diseases': [{'disease': d[0], 'count': d[1]} for d in diseases],
            'year': year,
            'month': month
        }
    
    def get_yearly_statistics(self, year=None):
        """إحصائيات سنوية"""
        if not year:
            year = datetime.now().year
        
        # إحصائيات شهرية للسنة
        monthly_data = []
        for month in range(1, 13):
            count = MedicalReport.query.filter(
                extract('year', MedicalReport.created_at) == year,
                extract('month', MedicalReport.created_at) == month
            ).count()
            monthly_data.append({'month': month, 'count': count})
        
        # إجمالي التقارير للسنة
        total_reports = MedicalReport.query.filter(
            extract('year', MedicalReport.created_at) == year
        ).count()
        
        # إحصائيات حسب نوع التقرير
        report_types = db.session.query(
            MedicalReport.report_type,
            func.count(MedicalReport.id).label('count')
        ).filter(
            extract('year', MedicalReport.created_at) == year
        ).group_by(MedicalReport.report_type).all()
        
        return {
            'total_reports': total_reports,
            'monthly_data': monthly_data,
            'report_types': [{'type': rt[0], 'count': rt[1]} for rt in report_types],
            'year': year
        }
    
    def get_disease_statistics(self, category=None):
        """إحصائيات الأمراض"""
        query = db.session.query(
            Disease.name,
            Disease.category,
            func.count(MedicalReport.id).label('count')
        ).join(MedicalReport)
        
        if category:
            query = query.filter(Disease.category == category)
        
        diseases = query.group_by(Disease.name, Disease.category)\
                       .order_by(func.count(MedicalReport.id).desc()).all()
        
        return [{'disease': d[0], 'category': d[1], 'count': d[2]} for d in diseases]
    
    def create_chart(self, data, chart_type='bar', title='', xlabel='', ylabel=''):
        """إنشاء رسم بياني"""
        plt.figure(figsize=(10, 6))
        
        if chart_type == 'bar':
            plt.bar(range(len(data)), [item['count'] for item in data])
            plt.xticks(range(len(data)), [item.get('type', item.get('disease', item.get('month', ''))) for item in data], rotation=45)
        elif chart_type == 'pie':
            labels = [item.get('type', item.get('disease', '')) for item in data]
            sizes = [item['count'] for item in data]
            plt.pie(sizes, labels=labels, autopct='%1.1f%%')
        elif chart_type == 'line':
            months = [item['month'] for item in data]
            counts = [item['count'] for item in data]
            plt.plot(months, counts, marker='o')
            plt.xticks(months)
        
        plt.title(title)
        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        plt.tight_layout()
        
        # تحويل الرسم إلى base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return chart_data
    
    def export_to_excel(self, data, filename):
        """تصدير البيانات إلى Excel"""
        try:
            # إنشاء DataFrame
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
            else:
                df = pd.DataFrame()
            
            # كتابة إلى Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='التقارير', index=False)
                
                # إضافة تنسيق
                workbook = writer.book
                worksheet = writer.sheets['التقارير']
                
                # تنسيق الرأس
                header_format = workbook.create_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })
                
                # تطبيق التنسيق على الرأس
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
            
            return True
        except Exception as e:
            print(f"خطأ في تصدير Excel: {e}")
            return False
    
    def generate_pdf_report(self, statistics, filename):
        """إنشاء تقرير PDF"""
        try:
            doc = SimpleDocTemplate(filename, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # إنشاء نمط للنص العربي
            arabic_style = ParagraphStyle(
                'Arabic',
                parent=styles['Normal'],
                fontName='Arabic' if 'Arabic' in [f.fontName for f in pdfmetrics.getRegisteredFontNames()] else 'Helvetica',
                fontSize=12,
                alignment=2  # محاذاة يمين
            )
            
            # العنوان
            title = Paragraph("تقرير إحصائيات التقارير الطبية", arabic_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # الإحصائيات العامة
            general_data = [
                ['البيان', 'القيمة'],
                ['إجمالي التقارير', str(statistics.get('total_reports', 0))],
                ['السنة', str(statistics.get('year', datetime.now().year))],
            ]
            
            if 'month' in statistics:
                general_data.append(['الشهر', str(statistics['month'])])
            
            general_table = Table(general_data)
            general_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(general_table)
            story.append(Spacer(1, 12))
            
            # إحصائيات أنواع التقارير
            if 'report_types' in statistics and statistics['report_types']:
                story.append(Paragraph("إحصائيات أنواع التقارير", arabic_style))
                story.append(Spacer(1, 6))
                
                report_data = [['نوع التقرير', 'العدد']]
                for rt in statistics['report_types']:
                    report_data.append([rt['type'], str(rt['count'])])
                
                report_table = Table(report_data)
                report_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(report_table)
            
            # بناء PDF
            doc.build(story)
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء PDF: {e}")
            return False
