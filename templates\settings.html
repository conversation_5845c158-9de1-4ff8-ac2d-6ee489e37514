{% extends "base.html" %}

{% block title %}الإعدادات - نظام أرشفة التقارير الطبية{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="bi bi-gear"></i>
                        إعدادات النظام
                    </h2>
                    <p class="card-text text-muted">
                        إدارة إعدادات البرنامج والتفضيلات العامة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- General Settings -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-sliders"></i>
                        الإعدادات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm">
                        <div class="mb-3">
                            <label for="defaultSavePath" class="form-label">
                                <i class="bi bi-folder"></i>
                                مجلد الحفظ الافتراضي
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="defaultSavePath" 
                                       value="{{ settings.get('default_save_path', './archives') }}" readonly>
                                <button type="button" class="btn btn-outline-secondary" onclick="selectFolder()">
                                    <i class="bi bi-folder-open"></i>
                                    تصفح
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="defaultFileType" class="form-label">
                                <i class="bi bi-file-earmark"></i>
                                نوع الملف الافتراضي
                            </label>
                            <select class="form-select" id="defaultFileType">
                                <option value="PDF" {{ 'selected' if settings.get('default_file_type') == 'PDF' else '' }}>PDF</option>
                                <option value="JPG" {{ 'selected' if settings.get('default_file_type') == 'JPG' else '' }}>JPG</option>
                                <option value="PNG" {{ 'selected' if settings.get('default_file_type') == 'PNG' else '' }}>PNG</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="defaultScanQuality" class="form-label">
                                <i class="bi bi-speedometer"></i>
                                جودة المسح الافتراضية
                            </label>
                            <select class="form-select" id="defaultScanQuality">
                                <option value="150dpi" {{ 'selected' if settings.get('default_scan_quality') == '150dpi' else '' }}>150 DPI</option>
                                <option value="300dpi" {{ 'selected' if settings.get('default_scan_quality') == '300dpi' else '' }}>300 DPI</option>
                                <option value="600dpi" {{ 'selected' if settings.get('default_scan_quality') == '600dpi' else '' }}>600 DPI</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="autoBackup" class="form-label">
                                <i class="bi bi-shield-check"></i>
                                النسخ الاحتياطي التلقائي
                            </label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoBackup">
                                <label class="form-check-label" for="autoBackup">
                                    تفعيل النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check"></i>
                            حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Database Management -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-database"></i>
                        إدارة قاعدة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>معلومات قاعدة البيانات:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-info-circle text-info"></i> نوع قاعدة البيانات: SQLite</li>
                            <li><i class="bi bi-file-earmark-text text-primary"></i> اسم الملف: medical_reports.db</li>
                            <li><i class="bi bi-hdd text-success"></i> حجم قاعدة البيانات: <span id="dbSize">جاري الحساب...</span></li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="backupDatabase()">
                            <i class="bi bi-download"></i>
                            نسخ احتياطي لقاعدة البيانات
                        </button>
                        
                        <button type="button" class="btn btn-warning" onclick="optimizeDatabase()">
                            <i class="bi bi-gear"></i>
                            تحسين قاعدة البيانات
                        </button>
                        
                        <button type="button" class="btn btn-danger" onclick="resetDatabase()">
                            <i class="bi bi-exclamation-triangle"></i>
                            إعادة تعيين قاعدة البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Disease Management -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-heart-pulse"></i>
                        إدارة الأمراض
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>أمراض ذوي الإعاقة:</h6>
                            <div id="disabilityDiseases" class="mb-3">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="addDisease('ذوي الإعاقة')">
                                <i class="bi bi-plus"></i>
                                إضافة مرض جديد
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>أمراض الرعاية الاجتماعية:</h6>
                            <div id="socialCareDiseases" class="mb-3">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="addDisease('الرعاية الاجتماعية')">
                                <i class="bi bi-plus"></i>
                                إضافة مرض جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>معلومات البرنامج:</h6>
                            <ul class="list-unstyled">
                                <li><strong>الاسم:</strong> نظام أرشفة التقارير الطبية</li>
                                <li><strong>الإصدار:</strong> 1.0.0</li>
                                <li><strong>التاريخ:</strong> 2024</li>
                                <li><strong>المطور:</strong> فريق التطوير</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>التقنيات المستخدمة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-code-square text-primary"></i> Python Flask</li>
                                <li><i class="bi bi-bootstrap text-purple"></i> Bootstrap 5</li>
                                <li><i class="bi bi-database text-success"></i> SQLite</li>
                                <li><i class="bi bi-graph-up text-info"></i> Chart.js</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>الدعم والمساعدة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-envelope text-primary"></i> البريد الإلكتروني: <EMAIL></li>
                                <li><i class="bi bi-telephone text-success"></i> الهاتف: +966 XX XXX XXXX</li>
                                <li><i class="bi bi-globe text-info"></i> الموقع: www.example.com</li>
                                <li><i class="bi bi-book text-warning"></i> دليل المستخدم</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Disease Modal -->
<div class="modal fade" id="addDiseaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i>
                    إضافة مرض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDiseaseForm">
                    <input type="hidden" id="diseaseCategory">
                    
                    <div class="mb-3">
                        <label for="diseaseName" class="form-label">اسم المرض *</label>
                        <input type="text" class="form-control" id="diseaseName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الفئة</label>
                        <input type="text" class="form-control" id="displayCategory" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNewDisease()">
                    <i class="bi bi-check"></i>
                    إضافة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحميل قائمة الأمراض
    loadDiseases();
    
    // حساب حجم قاعدة البيانات (محاكاة)
    setTimeout(() => {
        document.getElementById('dbSize').textContent = '2.5 MB';
    }, 1000);
    
    // إعداد نموذج الإعدادات العامة
    document.getElementById('generalSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveGeneralSettings();
    });
});

function loadDiseases() {
    // تحميل أمراض ذوي الإعاقة
    fetch('/api/diseases?category=ذوي الإعاقة')
        .then(response => response.json())
        .then(diseases => {
            displayDiseases('disabilityDiseases', diseases);
        })
        .catch(error => {
            console.error('خطأ في تحميل أمراض ذوي الإعاقة:', error);
            displayDiseases('disabilityDiseases', []);
        });
    
    // تحميل أمراض الرعاية الاجتماعية
    fetch('/api/diseases?category=الرعاية الاجتماعية')
        .then(response => response.json())
        .then(diseases => {
            displayDiseases('socialCareDiseases', diseases);
        })
        .catch(error => {
            console.error('خطأ في تحميل أمراض الرعاية الاجتماعية:', error);
            displayDiseases('socialCareDiseases', []);
        });
}

function displayDiseases(containerId, diseases) {
    const container = document.getElementById(containerId);
    
    if (diseases.length === 0) {
        container.innerHTML = '<p class="text-muted">لا توجد أمراض مضافة</p>';
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    diseases.forEach(disease => {
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>${disease.name}</span>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDisease(${disease.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function addDisease(category) {
    document.getElementById('diseaseCategory').value = category;
    document.getElementById('displayCategory').value = category;
    document.getElementById('diseaseName').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('addDiseaseModal'));
    modal.show();
}

function saveNewDisease() {
    const name = document.getElementById('diseaseName').value.trim();
    const category = document.getElementById('diseaseCategory').value;
    
    if (!name) {
        showAlert('يرجى إدخال اسم المرض', 'warning');
        return;
    }
    
    const data = {
        name: name,
        category: category
    };
    
    fetch('/api/diseases', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم إضافة المرض بنجاح!', 'success');
            
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addDiseaseModal'));
            modal.hide();
            
            // إعادة تحميل قائمة الأمراض
            loadDiseases();
        } else {
            showAlert('خطأ: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    });
}

function deleteDisease(diseaseId) {
    if (confirm('هل أنت متأكد من حذف هذا المرض؟')) {
        showAlert('ميزة حذف الأمراض ستكون متاحة قريباً', 'info');
    }
}

function saveGeneralSettings() {
    showLoading();
    
    // محاكاة حفظ الإعدادات
    setTimeout(() => {
        hideLoading();
        showAlert('تم حفظ الإعدادات بنجاح!', 'success');
    }, 2000);
}

function selectFolder() {
    showAlert('ميزة اختيار المجلد ستكون متاحة في الإصدار المكتبي', 'info');
}

function backupDatabase() {
    showLoading();
    
    setTimeout(() => {
        hideLoading();
        showAlert('تم إنشاء النسخة الاحتياطية بنجاح!', 'success');
    }, 3000);
}

function optimizeDatabase() {
    showLoading();
    
    setTimeout(() => {
        hideLoading();
        showAlert('تم تحسين قاعدة البيانات بنجاح!', 'success');
    }, 2000);
}

function resetDatabase() {
    if (confirm('تحذير: هذا الإجراء سيحذف جميع البيانات! هل أنت متأكد؟')) {
        if (confirm('هذا الإجراء لا يمكن التراجع عنه. هل تريد المتابعة؟')) {
            showLoading();
            
            setTimeout(() => {
                hideLoading();
                showAlert('تم إعادة تعيين قاعدة البيانات!', 'warning');
            }, 3000);
        }
    }
}
</script>
{% endblock %}
