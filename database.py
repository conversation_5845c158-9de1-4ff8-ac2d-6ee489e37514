from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Disease(db.Model):
    """نموذج الأمراض"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    category = db.Column(db.String(100), nullable=False)  # ذوي الإعاقة / الرعاية الاجتماعية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'created_at': self.created_at.isoformat()
        }

class MedicalReport(db.Model):
    """نموذج التقارير الطبية"""
    id = db.Column(db.Integer, primary_key=True)
    patient_name = db.Column(db.String(200), nullable=False)
    patient_id = db.Column(db.String(50), nullable=True)
    report_type = db.Column(db.String(100), nullable=False)  # ذوي الإعاقة / الرعاية الاجتماعية
    disease_id = db.Column(db.Integer, db.ForeignKey('disease.id'), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_type = db.Column(db.String(10), nullable=False)  # PDF / JPG / PNG
    scan_quality = db.Column(db.String(20), nullable=False)  # 150dpi, 300dpi, 600dpi
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text, nullable=True)
    
    # العلاقة مع الأمراض
    disease = db.relationship('Disease', backref=db.backref('reports', lazy=True))
    
    def to_dict(self):
        return {
            'id': self.id,
            'patient_name': self.patient_name,
            'patient_id': self.patient_id,
            'report_type': self.report_type,
            'disease': self.disease.name if self.disease else None,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'scan_quality': self.scan_quality,
            'created_at': self.created_at.isoformat(),
            'notes': self.notes
        }

class Settings(db.Model):
    """إعدادات البرنامج"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), nullable=False, unique=True)
    value = db.Column(db.Text, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

def init_database(app):
    """تهيئة قاعدة البيانات مع البيانات الأساسية"""
    db.init_app(app)
    
    with app.app_context():
        db.create_all()
        
        # إضافة الأمراض الأساسية إذا لم تكن موجودة
        if Disease.query.count() == 0:
            default_diseases = [
                # أمراض ذوي الإعاقة
                Disease(name='الشلل الدماغي', category='ذوي الإعاقة'),
                Disease(name='متلازمة داون', category='ذوي الإعاقة'),
                Disease(name='التوحد', category='ذوي الإعاقة'),
                Disease(name='الإعاقة الحركية', category='ذوي الإعاقة'),
                Disease(name='الإعاقة البصرية', category='ذوي الإعاقة'),
                Disease(name='الإعاقة السمعية', category='ذوي الإعاقة'),
                Disease(name='الإعاقة الذهنية', category='ذوي الإعاقة'),
                
                # أمراض الرعاية الاجتماعية
                Disease(name='أمراض القلب', category='الرعاية الاجتماعية'),
                Disease(name='السكري', category='الرعاية الاجتماعية'),
                Disease(name='ضغط الدم', category='الرعاية الاجتماعية'),
                Disease(name='أمراض الكلى', category='الرعاية الاجتماعية'),
                Disease(name='السرطان', category='الرعاية الاجتماعية'),
                Disease(name='أمراض الجهاز التنفسي', category='الرعاية الاجتماعية'),
                Disease(name='أمراض الجهاز الهضمي', category='الرعاية الاجتماعية'),
            ]
            
            for disease in default_diseases:
                db.session.add(disease)
            
            # إضافة الإعدادات الافتراضية
            default_settings = [
                Settings(key='default_save_path', value='./archives'),
                Settings(key='default_scan_quality', value='300dpi'),
                Settings(key='default_file_type', value='PDF'),
            ]
            
            for setting in default_settings:
                db.session.add(setting)
            
            db.session.commit()
            print("تم تهيئة قاعدة البيانات بنجاح!")
