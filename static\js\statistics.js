// ملف JavaScript للإحصائيات
// Statistics JavaScript Module

// متغيرات الرسوم البيانية
let monthlyTrendChart, reportTypesChart, disabilityDiseasesChart, socialCareDiseasesChart;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة الإحصائيات...');

    // تحديد الشهر الحالي
    const currentMonth = new Date().getMonth() + 1;
    const monthSelect = document.getElementById('monthSelect');
    if (monthSelect) {
        monthSelect.value = currentMonth;
    }

    // إخفاء نافذة التحميل فوراً
    try {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            const modalInstance = bootstrap.Modal.getInstance(loadingModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    } catch (e) {
        console.log('لا توجد نافذة تحميل لإخفائها');
    }

    // تحميل الإحصائيات بعد تأخير قصير
    setTimeout(() => {
        loadStatistics();
    }, 500);
});

// تحديث خيارات الفترة الزمنية
function updatePeriodOptions() {
    const periodType = document.getElementById('periodType').value;
    const monthContainer = document.getElementById('monthSelectContainer');

    if (periodType === 'monthly') {
        monthContainer.style.display = 'block';
    } else {
        monthContainer.style.display = 'none';
    }
}

// تحميل الإحصائيات
function loadStatistics() {
    console.log('بدء تحميل الإحصائيات...');

    const periodType = document.getElementById('periodType').value;
    const year = document.getElementById('yearSelect').value;
    const month = document.getElementById('monthSelect').value;

    showLoading();

    // بناء URL للAPI
    let apiUrl = `/api/statistics/${periodType}?year=${year}`;
    if (periodType === 'monthly') {
        apiUrl += `&month=${month}`;
    }

    console.log('استدعاء API:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('استجابة API:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('بيانات API:', data);

            // إخفاء نافذة التحميل
            try {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    const modalInstance = bootstrap.Modal.getInstance(loadingModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            } catch (e) {
                console.log('خطأ في إخفاء نافذة التحميل:', e);
            }

            if (data.success) {
                updateSummaryCards(data.data);
                updateCharts(data.data, periodType);
                updateDetailedTable(data.data, periodType);
                showAlert('تم تحديث الإحصائيات بنجاح!', 'success');
            } else {
                console.error('خطأ في API:', data.message);
                showAlert('خطأ في تحميل الإحصائيات: ' + data.message, 'danger');
                loadDemoData(periodType);
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة:', error);

            // إخفاء نافذة التحميل
            try {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    const modalInstance = bootstrap.Modal.getInstance(loadingModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            } catch (e) {
                console.log('خطأ في إخفاء نافذة التحميل:', e);
            }

            showAlert('خطأ في الاتصال، سيتم عرض بيانات تجريبية', 'warning');
            loadDemoData(periodType);
        });
}

// تحميل بيانات تجريبية
function loadDemoData(periodType) {
    console.log('تحميل بيانات تجريبية...');

    const demoData = {
        total_reports: 150,
        report_types: [
            { type: 'ذوي الإعاقة', count: 90 },
            { type: 'الرعاية الاجتماعية', count: 60 }
        ],
        monthly_data: [
            { month: 1, count: 10 }, { month: 2, count: 15 },
            { month: 3, count: 12 }, { month: 4, count: 18 },
            { month: 5, count: 20 }, { month: 6, count: 16 },
            { month: 7, count: 14 }, { month: 8, count: 22 },
            { month: 9, count: 8 }, { month: 10, count: 6 },
            { month: 11, count: 5 }, { month: 12, count: 4 }
        ]
    };

    updateSummaryCards(demoData);
    updateCharts(demoData, periodType);
    updateDetailedTable(demoData, periodType);
}

// تحديث بطاقات الملخص
function updateSummaryCards(data) {
    console.log('تحديث بطاقات الملخص...');

    const totalElement = document.getElementById('totalReportsCount');
    const disabilityElement = document.getElementById('disabilityCount');
    const socialCareElement = document.getElementById('socialCareCount');
    const averageElement = document.getElementById('monthlyAverage');

    if (totalElement) {
        totalElement.textContent = formatNumber(data.total_reports || 0);
    }

    let disabilityCount = 0;
    let socialCareCount = 0;

    if (data.report_types) {
        data.report_types.forEach(rt => {
            if (rt.type === 'ذوي الإعاقة') {
                disabilityCount = rt.count;
            } else if (rt.type === 'الرعاية الاجتماعية') {
                socialCareCount = rt.count;
            }
        });
    }

    if (disabilityElement) {
        disabilityElement.textContent = formatNumber(disabilityCount);
    }

    if (socialCareElement) {
        socialCareElement.textContent = formatNumber(socialCareCount);
    }

    // حساب المتوسط الشهري
    const monthlyAvg = data.monthly_data ?
        Math.round(data.monthly_data.reduce((sum, m) => sum + m.count, 0) / data.monthly_data.length) :
        Math.round((data.total_reports || 0) / 12);

    if (averageElement) {
        averageElement.textContent = formatNumber(monthlyAvg);
    }
}

// تحديث الرسوم البيانية
function updateCharts(data, periodType) {
    console.log('تحديث الرسوم البيانية...');

    try {
        updateMonthlyTrendChart(data);
        updateReportTypesChart(data);
        updateDiseasesCharts();
    } catch (error) {
        console.error('خطأ في تحديث الرسوم البيانية:', error);
    }
}

// تحديث رسم الاتجاه الشهري
function updateMonthlyTrendChart(data) {
    const ctx = document.getElementById('monthlyTrendChart');
    if (!ctx) return;

    if (monthlyTrendChart) {
        monthlyTrendChart.destroy();
    }

    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    const labels = data.monthly_data ?
        data.monthly_data.map(m => monthNames[m.month - 1]) :
        monthNames;

    const chartData = data.monthly_data ?
        data.monthly_data.map(m => m.count) :
        Array(12).fill(0);

    monthlyTrendChart = new Chart(ctx.getContext('2d'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد التقارير',
                data: chartData,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            }
        }
    });
}

// تحديث رسم توزيع أنواع التقارير
function updateReportTypesChart(data) {
    const ctx = document.getElementById('reportTypesChart');
    if (!ctx) return;

    if (reportTypesChart) {
        reportTypesChart.destroy();
    }

    const labels = data.report_types ? data.report_types.map(rt => rt.type) : [];
    const chartData = data.report_types ? data.report_types.map(rt => rt.count) : [];

    reportTypesChart = new Chart(ctx.getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: chartData,
                backgroundColor: ['#667eea', '#764ba2'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'bottom' }
            }
        }
    });
}

// تحديث رسوم الأمراض
function updateDiseasesCharts() {
    const disabilityDiseases = [
        { name: 'الشلل الدماغي', count: 25 },
        { name: 'متلازمة داون', count: 20 },
        { name: 'التوحد', count: 18 },
        { name: 'الإعاقة الحركية', count: 15 },
        { name: 'الإعاقة البصرية', count: 12 }
    ];

    const socialCareDiseases = [
        { name: 'أمراض القلب', count: 15 },
        { name: 'السكري', count: 12 },
        { name: 'ضغط الدم', count: 10 },
        { name: 'أمراض الكلى', count: 8 },
        { name: 'السرطان', count: 7 }
    ];

    updateDiseaseChart('disabilityDiseasesChart', disabilityDiseases);
    updateDiseaseChart('socialCareDiseasesChart', socialCareDiseases);
}

// تحديث رسم بياني للأمراض
function updateDiseaseChart(canvasId, diseases) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;

    if (window[canvasId + 'Instance']) {
        window[canvasId + 'Instance'].destroy();
    }

    window[canvasId + 'Instance'] = new Chart(ctx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: diseases.map(d => d.name),
            datasets: [{
                label: 'عدد التقارير',
                data: diseases.map(d => d.count),
                backgroundColor: '#667eea',
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                },
                x: {
                    ticks: { maxRotation: 45 }
                }
            }
        }
    });
}

// تحديث الجدول التفصيلي
function updateDetailedTable(data, periodType) {
    const tableBody = document.getElementById('detailedStatsTable');
    if (!tableBody) return;

    if (periodType === 'yearly' && data.monthly_data) {
        const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

        let tableHTML = '';
        data.monthly_data.forEach(monthData => {
            const monthName = monthNames[monthData.month - 1];
            const total = monthData.count;
            const disability = Math.round(total * 0.6);
            const socialCare = total - disability;
            const percentage = data.total_reports > 0 ? ((total / data.total_reports) * 100).toFixed(1) : 0;

            tableHTML += `
                <tr>
                    <td>${monthName}</td>
                    <td>${formatNumber(total)}</td>
                    <td>${formatNumber(disability)}</td>
                    <td>${formatNumber(socialCare)}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });

        tableBody.innerHTML = tableHTML;
    } else {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted">
                    لا توجد بيانات تفصيلية متاحة
                </td>
            </tr>
        `;
    }
}
