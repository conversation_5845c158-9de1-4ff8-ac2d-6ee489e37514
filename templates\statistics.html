{% extends "base.html" %}

{% block title %}الإحصائيات - نظام أرشفة التقارير الطبية{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title mb-1">
                            <i class="bi bi-graph-up"></i>
                            الإحصائيات والتقارير
                        </h2>
                        <p class="card-text text-muted mb-0">
                            عرض الإحصائيات التفصيلية والرسوم البيانية للتقارير الطبية
                        </p>
                    </div>
                    <div>
                        <a href="{{ url_for('export_pdf') }}" class="btn btn-danger">
                            <i class="bi bi-file-earmark-pdf"></i>
                            تصدير PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Period Selector -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-range"></i>
                        اختيار الفترة الزمنية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="periodType" class="form-label">نوع الفترة</label>
                            <select class="form-select" id="periodType" onchange="updatePeriodOptions()">
                                <option value="monthly">شهرية</option>
                                <option value="yearly" selected>سنوية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="yearSelect" class="form-label">السنة</label>
                            <select class="form-select" id="yearSelect">
                                <option value="2024" selected>2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3" id="monthSelectContainer" style="display: none;">
                            <label for="monthSelect" class="form-label">الشهر</label>
                            <select class="form-select" id="monthSelect">
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary" onclick="loadStatistics()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    تحديث الإحصائيات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4" id="summaryCards">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">إجمالي التقارير</h6>
                        <div class="stats-number" id="totalReportsCount">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">ذوي الإعاقة</h6>
                        <div class="stats-number" id="disabilityCount">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-person-wheelchair" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">الرعاية الاجتماعية</h6>
                        <div class="stats-number" id="socialCareCount">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-heart-pulse" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">متوسط شهري</h6>
                        <div class="stats-number" id="monthlyAverage">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-graph-up-arrow" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Monthly Trend Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up"></i>
                        الاتجاه الشهري للتقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Report Types Distribution -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart"></i>
                        توزيع أنواع التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="reportTypesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diseases Statistics -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i>
                        أكثر الأمراض شيوعاً - ذوي الإعاقة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="disabilityDiseasesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i>
                        أكثر الأمراض شيوعاً - الرعاية الاجتماعية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="socialCareDiseasesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table"></i>
                        إحصائيات تفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الفترة</th>
                                    <th>إجمالي التقارير</th>
                                    <th>ذوي الإعاقة</th>
                                    <th>الرعاية الاجتماعية</th>
                                    <th>النسبة المئوية</th>
                                </tr>
                            </thead>
                            <tbody id="detailedStatsTable">
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let monthlyTrendChart, reportTypesChart, disabilityDiseasesChart, socialCareDiseasesChart;

document.addEventListener('DOMContentLoaded', function() {
    // تحديد الشهر الحالي
    const currentMonth = new Date().getMonth() + 1;
    document.getElementById('monthSelect').value = currentMonth;
    
    // تحميل الإحصائيات الأولية
    loadStatistics();
});

function updatePeriodOptions() {
    const periodType = document.getElementById('periodType').value;
    const monthContainer = document.getElementById('monthSelectContainer');
    
    if (periodType === 'monthly') {
        monthContainer.style.display = 'block';
    } else {
        monthContainer.style.display = 'none';
    }
}

function loadStatistics() {
    const periodType = document.getElementById('periodType').value;
    const year = document.getElementById('yearSelect').value;
    const month = document.getElementById('monthSelect').value;
    
    showLoading();
    
    // بناء URL للAPI
    let apiUrl = `/api/statistics/${periodType}?year=${year}`;
    if (periodType === 'monthly') {
        apiUrl += `&month=${month}`;
    }
    
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                updateSummaryCards(data.data);
                updateCharts(data.data, periodType);
                updateDetailedTable(data.data, periodType);
            } else {
                showAlert('خطأ في تحميل الإحصائيات: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('خطأ:', error);
            // استخدام بيانات وهمية للعرض التوضيحي
            loadDemoData(periodType);
        });
}

function loadDemoData(periodType) {
    const demoData = {
        total_reports: 150,
        report_types: [
            { type: 'ذوي الإعاقة', count: 90 },
            { type: 'الرعاية الاجتماعية', count: 60 }
        ],
        monthly_data: [
            { month: 1, count: 10 },
            { month: 2, count: 15 },
            { month: 3, count: 12 },
            { month: 4, count: 18 },
            { month: 5, count: 20 },
            { month: 6, count: 16 },
            { month: 7, count: 14 },
            { month: 8, count: 22 },
            { month: 9, count: 8 },
            { month: 10, count: 6 },
            { month: 11, count: 5 },
            { month: 12, count: 4 }
        ]
    };
    
    updateSummaryCards(demoData);
    updateCharts(demoData, periodType);
    updateDetailedTable(demoData, periodType);
}

function updateSummaryCards(data) {
    document.getElementById('totalReportsCount').textContent = formatNumber(data.total_reports);
    
    let disabilityCount = 0;
    let socialCareCount = 0;
    
    data.report_types.forEach(rt => {
        if (rt.type === 'ذوي الإعاقة') {
            disabilityCount = rt.count;
        } else if (rt.type === 'الرعاية الاجتماعية') {
            socialCareCount = rt.count;
        }
    });
    
    document.getElementById('disabilityCount').textContent = formatNumber(disabilityCount);
    document.getElementById('socialCareCount').textContent = formatNumber(socialCareCount);
    
    // حساب المتوسط الشهري
    const monthlyAvg = data.monthly_data ? 
        Math.round(data.monthly_data.reduce((sum, m) => sum + m.count, 0) / data.monthly_data.length) : 
        Math.round(data.total_reports / 12);
    
    document.getElementById('monthlyAverage').textContent = formatNumber(monthlyAvg);
}

function updateCharts(data, periodType) {
    // رسم الاتجاه الشهري
    updateMonthlyTrendChart(data);
    
    // رسم توزيع أنواع التقارير
    updateReportTypesChart(data);
    
    // رسم الأمراض (بيانات وهمية)
    updateDiseasesCharts();
}

function updateMonthlyTrendChart(data) {
    const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
    
    if (monthlyTrendChart) {
        monthlyTrendChart.destroy();
    }
    
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    
    const labels = data.monthly_data ? 
        data.monthly_data.map(m => monthNames[m.month - 1]) : 
        monthNames;
    
    const chartData = data.monthly_data ? 
        data.monthly_data.map(m => m.count) : 
        Array(12).fill(0);
    
    monthlyTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد التقارير',
                data: chartData,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

function updateReportTypesChart(data) {
    const ctx = document.getElementById('reportTypesChart').getContext('2d');
    
    if (reportTypesChart) {
        reportTypesChart.destroy();
    }
    
    const labels = data.report_types.map(rt => rt.type);
    const chartData = data.report_types.map(rt => rt.count);
    
    reportTypesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: chartData,
                backgroundColor: ['#667eea', '#764ba2'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateDiseasesCharts() {
    // بيانات وهمية للأمراض
    const disabilityDiseases = [
        { name: 'الشلل الدماغي', count: 25 },
        { name: 'متلازمة داون', count: 20 },
        { name: 'التوحد', count: 18 },
        { name: 'الإعاقة الحركية', count: 15 },
        { name: 'الإعاقة البصرية', count: 12 }
    ];
    
    const socialCareDiseases = [
        { name: 'أمراض القلب', count: 15 },
        { name: 'السكري', count: 12 },
        { name: 'ضغط الدم', count: 10 },
        { name: 'أمراض الكلى', count: 8 },
        { name: 'السرطان', count: 7 }
    ];
    
    updateDiseaseChart('disabilityDiseasesChart', disabilityDiseases);
    updateDiseaseChart('socialCareDiseasesChart', socialCareDiseases);
}

function updateDiseaseChart(canvasId, diseases) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    
    // تدمير الرسم السابق إذا كان موجوداً
    if (window[canvasId + 'Instance']) {
        window[canvasId + 'Instance'].destroy();
    }
    
    window[canvasId + 'Instance'] = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: diseases.map(d => d.name),
            datasets: [{
                label: 'عدد التقارير',
                data: diseases.map(d => d.count),
                backgroundColor: '#667eea',
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

function updateDetailedTable(data, periodType) {
    const tableBody = document.getElementById('detailedStatsTable');
    
    if (periodType === 'yearly' && data.monthly_data) {
        const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        
        let tableHTML = '';
        data.monthly_data.forEach(monthData => {
            const monthName = monthNames[monthData.month - 1];
            const total = monthData.count;
            const disability = Math.round(total * 0.6); // تقدير
            const socialCare = total - disability;
            const percentage = data.total_reports > 0 ? ((total / data.total_reports) * 100).toFixed(1) : 0;
            
            tableHTML += `
                <tr>
                    <td>${monthName}</td>
                    <td>${formatNumber(total)}</td>
                    <td>${formatNumber(disability)}</td>
                    <td>${formatNumber(socialCare)}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = tableHTML;
    } else {
        // عرض إحصائيات شهرية أو بيانات أخرى
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted">
                    لا توجد بيانات تفصيلية متاحة
                </td>
            </tr>
        `;
    }
}
</script>
{% endblock %}
