#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام أرشفة التقارير الطبية
Medical Reports Archive System Test
"""

import unittest
import os
import tempfile
import shutil
from datetime import datetime
import sys

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestMedicalReportsSystem(unittest.TestCase):
    """فئة اختبار النظام"""

    def setUp(self):
        """إعداد الاختبار"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)

        # إنشاء المجلدات المطلوبة
        os.makedirs('archives', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        os.makedirs('templates', exist_ok=True)
        os.makedirs('static', exist_ok=True)

    def tearDown(self):
        """تنظيف بعد الاختبار"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_config_import(self):
        """اختبار استيراد ملف الإعدادات"""
        try:
            import config
            self.assertTrue(hasattr(config, 'DATABASE_URL'))
            self.assertTrue(hasattr(config, 'SECRET_KEY'))
            self.assertTrue(hasattr(config, 'UPLOAD_FOLDER'))
            print("✅ اختبار استيراد الإعدادات: نجح")
        except ImportError as e:
            self.fail(f"❌ فشل في استيراد ملف الإعدادات: {e}")

    def test_database_models(self):
        """اختبار نماذج قاعدة البيانات"""
        try:
            from database import Disease, MedicalReport, Settings

            # اختبار إنشاء مرض
            disease = Disease(name='اختبار', category='ذوي الإعاقة')
            self.assertEqual(disease.name, 'اختبار')
            self.assertEqual(disease.category, 'ذوي الإعاقة')

            print("✅ اختبار نماذج قاعدة البيانات: نجح")
        except ImportError as e:
            self.fail(f"❌ فشل في استيراد نماذج قاعدة البيانات: {e}")

    def test_scanner_module(self):
        """اختبار وحدة المسح الضوئي"""
        try:
            from scanner import DocumentScanner

            scanner = DocumentScanner()
            self.assertIsInstance(scanner.supported_formats, list)
            self.assertIsInstance(scanner.quality_settings, dict)

            # اختبار المحاكاة
            simulated_file = scanner.simulate_scan('PDF')
            if simulated_file:
                self.assertTrue(os.path.exists(simulated_file))
                os.unlink(simulated_file)  # حذف الملف المؤقت

            print("✅ اختبار وحدة المسح الضوئي: نجح")
        except ImportError as e:
            self.fail(f"❌ فشل في استيراد وحدة المسح الضوئي: {e}")

    def test_reports_module(self):
        """اختبار وحدة التقارير"""
        try:
            from reports import ReportGenerator

            generator = ReportGenerator()

            # اختبار إنشاء رسم بياني
            test_data = [
                {'type': 'ذوي الإعاقة', 'count': 10},
                {'type': 'الرعاية الاجتماعية', 'count': 5}
            ]

            chart_data = generator.create_chart(test_data, 'bar', 'اختبار')
            self.assertIsInstance(chart_data, str)

            print("✅ اختبار وحدة التقارير: نجح")
        except ImportError as e:
            self.fail(f"❌ فشل في استيراد وحدة التقارير: {e}")

    def test_logger_module(self):
        """اختبار وحدة السجلات"""
        try:
            from logger import MedicalReportsLogger, log_info

            logger = MedicalReportsLogger('test')
            logger.info('رسالة اختبار')

            log_info('اختبار الدالة المساعدة')

            print("✅ اختبار وحدة السجلات: نجح")
        except ImportError as e:
            self.fail(f"❌ فشل في استيراد وحدة السجلات: {e}")

    def test_flask_app_creation(self):
        """اختبار إنشاء تطبيق Flask"""
        try:
            # نسخ الملفات المطلوبة للاختبار
            import shutil
            source_files = [
                'app.py', 'database.py', 'scanner.py',
                'reports.py', 'config.py', 'logger.py'
            ]

            for file in source_files:
                if os.path.exists(os.path.join(self.original_cwd, file)):
                    shutil.copy2(
                        os.path.join(self.original_cwd, file),
                        os.path.join(self.test_dir, file)
                    )

            # نسخ مجلد templates
            templates_src = os.path.join(self.original_cwd, 'templates')
            if os.path.exists(templates_src):
                shutil.copytree(templates_src, 'templates', dirs_exist_ok=True)

            from app import app

            self.assertIsNotNone(app)
            self.assertEqual(app.config['TESTING'], False)

            print("✅ اختبار إنشاء تطبيق Flask: نجح")
        except Exception as e:
            print(f"⚠️  تحذير: لا يمكن اختبار تطبيق Flask: {e}")

    def test_required_packages(self):
        """اختبار وجود الحزم المطلوبة"""
        required_packages = [
            'flask', 'flask_sqlalchemy', 'PIL', 'reportlab',
            'pandas', 'openpyxl', 'matplotlib'
        ]

        missing_packages = []

        for package in required_packages:
            try:
                if package == 'PIL':
                    import PIL
                elif package == 'flask_sqlalchemy':
                    import flask_sqlalchemy
                else:
                    __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            self.fail(f"❌ الحزم المفقودة: {', '.join(missing_packages)}")
        else:
            print("✅ اختبار الحزم المطلوبة: جميع الحزم متوفرة")

    def test_file_structure(self):
        """اختبار هيكل الملفات"""
        required_files = [
            'app.py', 'database.py', 'scanner.py', 'reports.py',
            'config.py', 'logger.py', 'requirements.txt', 'README.md'
        ]

        required_dirs = [
            'templates', 'static', 'archives', 'logs'
        ]

        missing_files = []
        missing_dirs = []

        # التحقق من الملفات
        for file in required_files:
            if not os.path.exists(os.path.join(self.original_cwd, file)):
                missing_files.append(file)

        # التحقق من المجلدات
        for dir in required_dirs:
            if not os.path.exists(os.path.join(self.original_cwd, dir)):
                missing_dirs.append(dir)

        if missing_files or missing_dirs:
            error_msg = ""
            if missing_files:
                error_msg += f"ملفات مفقودة: {', '.join(missing_files)}\n"
            if missing_dirs:
                error_msg += f"مجلدات مفقودة: {', '.join(missing_dirs)}"
            self.fail(f"❌ {error_msg}")
        else:
            print("✅ اختبار هيكل الملفات: جميع الملفات والمجلدات موجودة")

def run_system_check():
    """تشغيل فحص شامل للنظام"""
    print("=" * 60)
    print("🔍 فحص نظام أرشفة التقارير الطبية")
    print("   Medical Reports Archive System Check")
    print("=" * 60)
    print()

    # تشغيل الاختبارات
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMedicalReportsSystem)
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)

    print()
    print("📊 نتائج الفحص:")
    print(f"   ✅ اختبارات نجحت: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   ❌ اختبارات فشلت: {len(result.failures)}")
    print(f"   🚫 أخطاء: {len(result.errors)}")

    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            error_msg = traceback.split('AssertionError: ')[-1].split('\n')[0]
            print(f"   - {test}: {error_msg}")

    if result.errors:
        print("\n🚫 الأخطاء:")
        for test, traceback in result.errors:
            error_msg = traceback.split('\n')[-2]
            print(f"   - {test}: {error_msg}")

    print()

    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    success = run_system_check()

    print()
    print("=" * 60)

    if success:
        print("✅ النظام جاهز للتشغيل!")
        print("📋 للتشغيل:")
        print("   python app.py")
        print("   ثم افتح المتصفح على: http://localhost:5000")
    else:
        print("❌ يرجى إصلاح الأخطاء قبل التشغيل")

    print("=" * 60)

if __name__ == "__main__":
    main()
