#!/bin/bash

echo "========================================"
echo "    نظام أرشفة التقارير الطبية"
echo "========================================"
echo

echo "جاري تشغيل النظام..."
echo

# تحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت على النظام"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# تحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    echo "خطأ: pip3 غير مثبت على النظام"
    echo "يرجى تثبيت pip3"
    exit 1
fi

# تحقق من وجود المكتبات المطلوبة
echo "جاري التحقق من المكتبات المطلوبة..."
if ! pip3 show Flask &> /dev/null; then
    echo "جاري تثبيت المكتبات المطلوبة..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "خطأ في تثبيت المكتبات"
        exit 1
    fi
fi

# إنشاء مجلد الأرشيف إذا لم يكن موجوداً
if [ ! -d "archives" ]; then
    mkdir archives
fi

echo
echo "تم تشغيل النظام بنجاح!"
echo "يمكنك الوصول للنظام عبر المتصفح على العنوان:"
echo "http://localhost:5000"
echo
echo "للإيقاف: اضغط Ctrl+C"
echo "========================================"
echo

# تشغيل التطبيق
python3 app.py
