{% extends "base.html" %}

{% block title %}المسح الضوئي - نظام أرشفة التقارير الطبية{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="bi bi-scanner"></i>
                        المسح الضوئي للتقارير الطبية
                    </h2>
                    <p class="card-text text-muted">
                        قم بمسح التقارير الطبية ضوئياً وحفظها في النظام مع البيانات المطلوبة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scan Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-form"></i>
                        بيانات التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form id="scanForm">
                        <div class="row">
                            <!-- بيانات المريض -->
                            <div class="col-md-6 mb-3">
                                <label for="patientName" class="form-label">
                                    <i class="bi bi-person"></i>
                                    اسم المريض *
                                </label>
                                <input type="text" class="form-control" id="patientName" name="patient_name" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="patientId" class="form-label">
                                    <i class="bi bi-card-text"></i>
                                    رقم المريض التعريفي
                                </label>
                                <input type="text" class="form-control" id="patientId" name="patient_id">
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع التقرير -->
                            <div class="col-md-6 mb-3">
                                <label for="reportType" class="form-label">
                                    <i class="bi bi-file-earmark-text"></i>
                                    نوع التقرير *
                                </label>
                                <select class="form-select" id="reportType" name="report_type" required>
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="ذوي الإعاقة">ذوي الإعاقة</option>
                                    <option value="الرعاية الاجتماعية">الرعاية الاجتماعية</option>
                                </select>
                            </div>
                            
                            <!-- المرض -->
                            <div class="col-md-6 mb-3">
                                <label for="disease" class="form-label">
                                    <i class="bi bi-heart-pulse"></i>
                                    المرض *
                                </label>
                                <div class="input-group">
                                    <select class="form-select" id="disease" name="disease_id" required>
                                        <option value="">اختر المرض</option>
                                        {% for disease in diseases %}
                                        <option value="{{ disease.id }}" data-category="{{ disease.category }}">
                                            {{ disease.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#addDiseaseModal">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- إعدادات المسح -->
                            <div class="col-md-4 mb-3">
                                <label for="fileType" class="form-label">
                                    <i class="bi bi-file-earmark"></i>
                                    نوع الملف
                                </label>
                                <select class="form-select" id="fileType" name="file_type">
                                    <option value="PDF">PDF</option>
                                    <option value="JPG">JPG</option>
                                    <option value="PNG">PNG</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="scanQuality" class="form-label">
                                    <i class="bi bi-speedometer"></i>
                                    جودة المسح
                                </label>
                                <select class="form-select" id="scanQuality" name="scan_quality">
                                    <option value="150dpi">150 DPI</option>
                                    <option value="300dpi" selected>300 DPI</option>
                                    <option value="600dpi">600 DPI</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="scanner" class="form-label">
                                    <i class="bi bi-printer"></i>
                                    الماسح الضوئي
                                </label>
                                <select class="form-select" id="scanner" name="scanner_id">
                                    <option value="">الافتراضي</option>
                                    {% for scanner in scanners %}
                                    <option value="{{ scanner.id }}">{{ scanner.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- مكان الحفظ -->
                        <div class="mb-3">
                            <label for="savePath" class="form-label">
                                <i class="bi bi-folder"></i>
                                مكان الحفظ
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="savePath" name="save_path" value="./archives" readonly>
                                <button type="button" class="btn btn-outline-secondary" onclick="selectSavePath()">
                                    <i class="bi bi-folder-open"></i>
                                    تصفح
                                </button>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">
                                <i class="bi bi-chat-text"></i>
                                ملاحظات
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="resetForm()">
                                <i class="bi bi-arrow-clockwise"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-scanner"></i>
                                بدء المسح الضوئي
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معاينة وإرشادات -->
        <div class="col-lg-4">
            <!-- إرشادات المسح -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightbulb"></i>
                        إرشادات المسح الضوئي
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            تأكد من وضع المستند بشكل مستقيم
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            استخدم جودة 300 DPI للاستخدام العادي
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            استخدم جودة 600 DPI للنصوص الصغيرة
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            تنسيق PDF مناسب للأرشفة طويلة المدى
                        </li>
                    </ul>
                </div>
            </div>

            <!-- معلومات الماسح الضوئي -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i>
                        حالة الماسح الضوئي
                    </h6>
                </div>
                <div class="card-body">
                    <div id="scannerStatus">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">جاري الفحص...</span>
                            </div>
                            <p class="mt-2 mb-0">جاري فحص الماسحات الضوئية...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة مرض جديد -->
<div class="modal fade" id="addDiseaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i>
                    إضافة مرض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDiseaseForm">
                    <div class="mb-3">
                        <label for="diseaseName" class="form-label">اسم المرض *</label>
                        <input type="text" class="form-control" id="diseaseName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="diseaseCategory" class="form-label">الفئة *</label>
                        <select class="form-select" id="diseaseCategory" name="category" required>
                            <option value="">اختر الفئة</option>
                            <option value="ذوي الإعاقة">ذوي الإعاقة</option>
                            <option value="الرعاية الاجتماعية">الرعاية الاجتماعية</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addDisease()">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="scanProgressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-scanner"></i>
                    جاري المسح الضوئي
                </h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري المسح...</span>
                    </div>
                </div>
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <p class="text-center mb-0" id="scanStatus">جاري تهيئة الماسح الضوئي...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث قائمة الأمراض عند تغيير نوع التقرير
    document.getElementById('reportType').addEventListener('change', function() {
        filterDiseases(this.value);
    });
    
    // فحص حالة الماسحات الضوئية
    checkScannerStatus();
    
    // إعداد نموذج المسح
    document.getElementById('scanForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startScan();
    });
});

function filterDiseases(reportType) {
    const diseaseSelect = document.getElementById('disease');
    const options = diseaseSelect.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') {
            option.style.display = 'block';
            return;
        }
        
        const category = option.getAttribute('data-category');
        if (!reportType || category === reportType) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
    
    // إعادة تعيين الاختيار
    diseaseSelect.value = '';
}

function checkScannerStatus() {
    const statusDiv = document.getElementById('scannerStatus');
    
    // محاكاة فحص الماسحات الضوئية
    setTimeout(() => {
        const scannersCount = {{ scanners|length }};
        
        if (scannersCount > 0) {
            statusDiv.innerHTML = `
                <div class="alert alert-success mb-0">
                    <i class="bi bi-check-circle"></i>
                    تم العثور على ${scannersCount} ماسح ضوئي
                </div>
            `;
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-warning mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    لم يتم العثور على ماسحات ضوئية<br>
                    <small>سيتم استخدام المحاكاة للاختبار</small>
                </div>
            `;
        }
    }, 2000);
}

function startScan() {
    const form = document.getElementById('scanForm');
    const formData = new FormData(form);
    
    // تحويل البيانات إلى JSON
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    // عرض نافذة التقدم
    const progressModal = new bootstrap.Modal(document.getElementById('scanProgressModal'));
    progressModal.show();
    
    // محاكاة تقدم المسح
    simulateScanProgress();
    
    // إرسال طلب المسح
    fetch('/api/scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        progressModal.hide();
        
        if (result.success) {
            showAlert('تم المسح والحفظ بنجاح!', 'success');
            resetForm();
        } else {
            showAlert('خطأ: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        progressModal.hide();
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    });
}

function simulateScanProgress() {
    const progressBar = document.querySelector('#scanProgressModal .progress-bar');
    const statusText = document.getElementById('scanStatus');
    
    const steps = [
        { progress: 20, text: 'جاري تهيئة الماسح الضوئي...' },
        { progress: 40, text: 'جاري مسح المستند...' },
        { progress: 60, text: 'جاري معالجة الصورة...' },
        { progress: 80, text: 'جاري حفظ الملف...' },
        { progress: 100, text: 'تم الانتهاء!' }
    ];
    
    let currentStep = 0;
    
    const interval = setInterval(() => {
        if (currentStep < steps.length) {
            const step = steps[currentStep];
            progressBar.style.width = step.progress + '%';
            statusText.textContent = step.text;
            currentStep++;
        } else {
            clearInterval(interval);
        }
    }, 1000);
}

function addDisease() {
    const form = document.getElementById('addDiseaseForm');
    const formData = new FormData(form);
    
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    fetch('/api/diseases', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // إضافة المرض الجديد إلى القائمة
            const diseaseSelect = document.getElementById('disease');
            const option = document.createElement('option');
            option.value = result.disease.id;
            option.textContent = result.disease.name;
            option.setAttribute('data-category', result.disease.category);
            diseaseSelect.appendChild(option);
            
            // اختيار المرض الجديد
            diseaseSelect.value = result.disease.id;
            
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addDiseaseModal'));
            modal.hide();
            
            // إعادة تعيين النموذج
            form.reset();
            
            showAlert('تم إضافة المرض بنجاح!', 'success');
        } else {
            showAlert('خطأ: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    });
}

function selectSavePath() {
    // في التطبيق الحقيقي، يمكن استخدام Electron أو API خاص لاختيار المجلد
    showAlert('ميزة اختيار المجلد ستكون متاحة في الإصدار المكتبي', 'info');
}

function resetForm() {
    document.getElementById('scanForm').reset();
    document.getElementById('savePath').value = './archives';
}
</script>
{% endblock %}
