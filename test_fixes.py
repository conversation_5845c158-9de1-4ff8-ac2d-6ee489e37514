#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الإصلاحات الجديدة
Test New Fixes
"""

import requests
import json
import time

def test_statistics_api():
    """اختبار API الإحصائيات"""
    print("🔍 اختبار API الإحصائيات...")
    
    try:
        # اختبار الإحصائيات السنوية
        response = requests.get('http://127.0.0.1:5000/api/statistics/yearly?year=2024')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API الإحصائيات السنوية يعمل بشكل صحيح")
                print(f"   إجمالي التقارير: {data['data'].get('total_reports', 0)}")
            else:
                print("❌ API الإحصائيات يرجع خطأ:", data.get('message'))
        else:
            print(f"❌ خطأ في HTTP: {response.status_code}")
    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")

def test_reports_api():
    """اختبار API التقارير"""
    print("\n🔍 اختبار API التقارير...")
    
    try:
        # اختبار الحصول على قائمة التقارير
        response = requests.get('http://127.0.0.1:5000/reports')
        if response.status_code == 200:
            print("✅ صفحة التقارير تعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في صفحة التقارير: {response.status_code}")
    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")

def test_scan_api():
    """اختبار API المسح الضوئي"""
    print("\n🔍 اختبار API المسح الضوئي...")
    
    try:
        # بيانات تجريبية للمسح
        test_data = {
            "patient_name": "مريض اختبار",
            "patient_id": "TEST123",
            "report_type": "ذوي الإعاقة",
            "disease_id": 1,
            "file_type": "PDF",
            "scan_quality": "300dpi",
            "notes": "تقرير اختبار تلقائي"
        }
        
        response = requests.post(
            'http://127.0.0.1:5000/api/scan',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API المسح الضوئي يعمل بشكل صحيح")
                print(f"   تم إنشاء التقرير رقم: {data.get('report_id')}")
                return data.get('report_id')
            else:
                print("❌ API المسح يرجع خطأ:", data.get('message'))
        else:
            print(f"❌ خطأ في HTTP: {response.status_code}")
    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    return None

def test_report_operations(report_id):
    """اختبار عمليات التقرير (عرض، تحديث، حذف)"""
    if not report_id:
        print("\n⚠️  تخطي اختبار عمليات التقرير - لا يوجد تقرير للاختبار")
        return
    
    print(f"\n🔍 اختبار عمليات التقرير رقم {report_id}...")
    
    try:
        # اختبار عرض التقرير
        response = requests.get(f'http://127.0.0.1:5000/api/reports/{report_id}')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ عرض التقرير يعمل بشكل صحيح")
            else:
                print("❌ خطأ في عرض التقرير:", data.get('message'))
        
        # اختبار تحديث التقرير
        update_data = {
            "patient_name": "مريض اختبار محدث",
            "notes": "تم تحديث الملاحظات"
        }
        
        response = requests.put(
            f'http://127.0.0.1:5000/api/reports/{report_id}',
            json=update_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ تحديث التقرير يعمل بشكل صحيح")
            else:
                print("❌ خطأ في تحديث التقرير:", data.get('message'))
        
        # اختبار حذف التقرير
        response = requests.delete(f'http://127.0.0.1:5000/api/reports/{report_id}')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ حذف التقرير يعمل بشكل صحيح")
            else:
                print("❌ خطأ في حذف التقرير:", data.get('message'))
    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")

def test_diseases_api():
    """اختبار API الأمراض"""
    print("\n🔍 اختبار API الأمراض...")
    
    try:
        # اختبار الحصول على قائمة الأمراض
        response = requests.get('http://127.0.0.1:5000/api/diseases')
        if response.status_code == 200:
            diseases = response.json()
            print(f"✅ API الأمراض يعمل بشكل صحيح - تم العثور على {len(diseases)} مرض")
        else:
            print(f"❌ خطأ في API الأمراض: {response.status_code}")
        
        # اختبار إضافة مرض جديد
        new_disease = {
            "name": "مرض اختبار",
            "category": "ذوي الإعاقة"
        }
        
        response = requests.post(
            'http://127.0.0.1:5000/api/diseases',
            json=new_disease,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ إضافة مرض جديد يعمل بشكل صحيح")
            else:
                print("❌ خطأ في إضافة المرض:", data.get('message'))
    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")

def test_static_files():
    """اختبار الملفات الثابتة"""
    print("\n🔍 اختبار الملفات الثابتة...")
    
    static_files = [
        '/static/js/common.js',
        '/static/js/statistics.js'
    ]
    
    for file_path in static_files:
        try:
            response = requests.get(f'http://127.0.0.1:5000{file_path}')
            if response.status_code == 200:
                print(f"✅ {file_path} متاح")
            else:
                print(f"❌ {file_path} غير متاح: {response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في تحميل {file_path}: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الإصلاحات الجديدة")
    print("   Testing New Fixes")
    print("=" * 60)
    
    # التحقق من تشغيل الخادم
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم لا يعمل بشكل صحيح")
            return
    except Exception as e:
        print(f"❌ لا يمكن الوصول للخادم: {e}")
        print("تأكد من تشغيل الخادم بـ: python app.py")
        return
    
    print("✅ الخادم يعمل بشكل صحيح")
    
    # تشغيل الاختبارات
    test_static_files()
    test_statistics_api()
    test_diseases_api()
    test_reports_api()
    
    # اختبار المسح والعمليات
    report_id = test_scan_api()
    test_report_operations(report_id)
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار!")
    print("=" * 60)

if __name__ == "__main__":
    main()
