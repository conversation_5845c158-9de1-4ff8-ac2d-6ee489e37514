#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشاكل الماسحات الضوئية
Scanner Diagnostic Tool
"""

import platform
import subprocess
import sys
import os

def check_system_info():
    """عرض معلومات النظام"""
    print("🖥️  معلومات النظام:")
    print(f"   نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"   المعمارية: {platform.architecture()[0]}")
    print(f"   Python: {sys.version.split()[0]}")
    print()

def check_windows_scanners():
    """فحص الماسحات في Windows"""
    print("🔍 فحص الماسحات الضوئية في Windows...")
    
    # التحقق من مكتبة pywin32
    try:
        import win32com.client
        print("✅ مكتبة win32com متوفرة")
    except ImportError:
        print("❌ مكتبة win32com غير متوفرة")
        print("   للتثبيت: pip install pywin32")
        return False
    
    # البحث عن الماسحات باستخدام WIA
    try:
        wia = win32com.client.Dispatch("WIA.DeviceManager")
        devices = wia.DeviceInfos
        
        print(f"📱 عدد الأجهزة المكتشفة: {devices.Count}")
        
        scanner_count = 0
        for i in range(1, devices.Count + 1):
            device = devices.Item(i)
            device_type = device.Type
            device_name = device.Properties("Name").Value
            
            print(f"   الجهاز {i}:")
            print(f"     الاسم: {device_name}")
            print(f"     النوع: {device_type}")
            
            if device_type == 1:  # Scanner type
                scanner_count += 1
                print(f"     ✅ ماسح ضوئي")
            else:
                print(f"     ℹ️  جهاز آخر")
        
        print(f"\n📊 إجمالي الماسحات الضوئية: {scanner_count}")
        return scanner_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في الوصول لـ WIA: {e}")
        return False

def check_linux_scanners():
    """فحص الماسحات في Linux"""
    print("🔍 فحص الماسحات الضوئية في Linux...")
    
    # التحقق من SANE
    try:
        result = subprocess.run(['scanimage', '-L'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            output = result.stdout.strip()
            if output:
                print("✅ SANE يعمل بشكل صحيح")
                print("📱 الماسحات المكتشفة:")
                for line in output.split('\n'):
                    if line.strip():
                        print(f"   {line}")
                return True
            else:
                print("⚠️  SANE يعمل لكن لم يتم العثور على ماسحات")
                return False
        else:
            print(f"❌ خطأ في SANE: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ SANE غير مثبت")
        print("   للتثبيت في Ubuntu: sudo apt-get install sane-utils")
        return False
    except subprocess.TimeoutExpired:
        print("⏱️  انتهت مهلة البحث عن الماسحات")
        return False
    
    # التحقق من مكتبة pyinsane2
    try:
        import pyinsane2
        print("✅ مكتبة pyinsane2 متوفرة")
        
        pyinsane2.init()
        devices = pyinsane2.get_devices()
        
        print(f"📱 عدد الماسحات المكتشفة: {len(devices)}")
        for i, device in enumerate(devices):
            print(f"   الماسح {i+1}: {device.nice_name}")
        
        return len(devices) > 0
        
    except ImportError:
        print("❌ مكتبة pyinsane2 غير متوفرة")
        print("   للتثبيت: pip install pyinsane2")
        return False
    except Exception as e:
        print(f"❌ خطأ في pyinsane2: {e}")
        return False

def check_usb_devices():
    """فحص أجهزة USB"""
    print("🔌 فحص أجهزة USB...")
    
    system = platform.system()
    
    if system == "Windows":
        try:
            result = subprocess.run(['wmic', 'path', 'Win32_USBHub', 'get', 'Name'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                devices = [line.strip() for line in result.stdout.split('\n') 
                          if line.strip() and line.strip() != 'Name']
                print(f"📱 عدد أجهزة USB: {len(devices)}")
                for device in devices[:5]:  # عرض أول 5 أجهزة
                    print(f"   {device}")
                if len(devices) > 5:
                    print(f"   ... و {len(devices) - 5} جهاز آخر")
        except Exception as e:
            print(f"❌ خطأ في فحص USB: {e}")
    
    elif system == "Linux":
        try:
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                devices = result.stdout.strip().split('\n')
                print(f"📱 عدد أجهزة USB: {len(devices)}")
                
                # البحث عن ماسحات ضوئية
                scanner_keywords = ['scanner', 'scan', 'canon', 'hp', 'epson', 'brother']
                potential_scanners = []
                
                for device in devices:
                    device_lower = device.lower()
                    if any(keyword in device_lower for keyword in scanner_keywords):
                        potential_scanners.append(device)
                
                if potential_scanners:
                    print("🔍 أجهزة محتملة للمسح الضوئي:")
                    for scanner in potential_scanners:
                        print(f"   {scanner}")
                else:
                    print("⚠️  لم يتم العثور على ماسحات ضوئية في USB")
                    
        except Exception as e:
            print(f"❌ خطأ في فحص USB: {e}")

def check_permissions():
    """فحص الصلاحيات"""
    print("🔐 فحص الصلاحيات...")
    
    system = platform.system()
    
    if system == "Linux":
        # فحص مجموعات المستخدم
        try:
            import grp
            import pwd
            
            username = pwd.getpwuid(os.getuid()).pw_name
            user_groups = [g.gr_name for g in grp.getgrall() if username in g.gr_mem]
            
            print(f"👤 المستخدم: {username}")
            print(f"👥 المجموعات: {', '.join(user_groups)}")
            
            if 'scanner' in user_groups:
                print("✅ المستخدم في مجموعة scanner")
            else:
                print("❌ المستخدم ليس في مجموعة scanner")
                print("   للإضافة: sudo usermod -a -G scanner $USER")
            
            if 'lp' in user_groups:
                print("✅ المستخدم في مجموعة lp")
            else:
                print("⚠️  المستخدم ليس في مجموعة lp")
                
        except Exception as e:
            print(f"❌ خطأ في فحص الصلاحيات: {e}")

def provide_solutions():
    """تقديم الحلول"""
    print("\n" + "="*60)
    print("💡 الحلول المقترحة:")
    print("="*60)
    
    system = platform.system()
    
    if system == "Windows":
        print("""
🪟 حلول Windows:

1. تثبيت مكتبة pywin32:
   pip install pywin32

2. تحديث تعريفات الماسح الضوئي:
   - اذهب لموقع الشركة المصنعة
   - حمل أحدث التعريفات
   - تأكد من تشغيل Windows Image Acquisition service

3. إعادة تشغيل خدمة WIA:
   - افتح Services.msc
   - ابحث عن "Windows Image Acquisition"
   - أعد تشغيل الخدمة

4. اختبار الماسح في Windows:
   - افتح "Windows Fax and Scan"
   - جرب مسح تجريبي
        """)
    
    elif system == "Linux":
        print("""
🐧 حلول Linux:

1. تثبيت SANE:
   sudo apt-get install sane-utils libsane-dev python3-dev
   
2. تثبيت pyinsane2:
   pip install pyinsane2

3. إضافة المستخدم للمجموعات:
   sudo usermod -a -G scanner,lp $USER
   
4. إعادة تشغيل خدمة SANE:
   sudo systemctl restart saned

5. اختبار الماسح:
   scanimage -L
   scanimage --test
        """)
    
    print("""
🔧 حلول عامة:

1. التحقق من التوصيل:
   - تأكد من توصيل الماسح بـ USB
   - جرب منفذ USB آخر
   - تأكد من تشغيل الماسح

2. إعادة تشغيل:
   - أعد تشغيل الماسح الضوئي
   - أعد تشغيل الكمبيوتر

3. اختبار مع برامج أخرى:
   - جرب برنامج المسح الأصلي للماسح
   - تأكد من عمل الماسح خارج البرنامج

4. استخدام المحاكاة:
   - البرنامج يدعم المحاكاة للاختبار
   - يمكن استخدامه بدون ماسح ضوئي
    """)

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🔍 أداة تشخيص الماسحات الضوئية")
    print("   Scanner Diagnostic Tool")
    print("="*60)
    print()
    
    check_system_info()
    
    system = platform.system()
    scanners_found = False
    
    if system == "Windows":
        scanners_found = check_windows_scanners()
    elif system == "Linux":
        scanners_found = check_linux_scanners()
    else:
        print(f"⚠️  نظام التشغيل {system} غير مدعوم بالكامل")
    
    print()
    check_usb_devices()
    print()
    check_permissions()
    
    print("\n" + "="*60)
    if scanners_found:
        print("🎉 تم العثور على ماسحات ضوئية!")
        print("   يجب أن يعمل المسح الضوئي في البرنامج الآن.")
    else:
        print("❌ لم يتم العثور على ماسحات ضوئية")
        print("   سيتم استخدام المحاكاة في البرنامج.")
    
    provide_solutions()

if __name__ == "__main__":
    main()
