{% extends "base.html" %}

{% block title %}التقارير - نظام أرشفة التقارير الطبية{% endblock %}

{% block content %}
<div class="col-12">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title mb-1">
                            <i class="bi bi-file-earmark-text"></i>
                            إدارة التقارير الطبية
                        </h2>
                        <p class="card-text text-muted mb-0">
                            عرض وإدارة جميع التقارير الطبية المحفوظة في النظام
                        </p>
                    </div>
                    <div>
                        <a href="{{ url_for('scan_page') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            تقرير جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel"></i>
                        البحث والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form id="filterForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="searchPatient" class="form-label">اسم المريض</label>
                            <input type="text" class="form-control" id="searchPatient" placeholder="ابحث عن مريض...">
                        </div>

                        <div class="col-md-3">
                            <label for="filterReportType" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="filterReportType">
                                <option value="">جميع الأنواع</option>
                                <option value="ذوي الإعاقة">ذوي الإعاقة</option>
                                <option value="الرعاية الاجتماعية">الرعاية الاجتماعية</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="filterDateFrom" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="filterDateFrom">
                        </div>

                        <div class="col-md-3">
                            <label for="filterDateTo" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="filterDateTo">
                        </div>

                        <div class="col-12">
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="bi bi-search"></i>
                                بحث
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="bi bi-x-circle"></i>
                                مسح الفلاتر
                            </button>
                            <div class="float-end">
                                <a href="{{ url_for('export_excel') }}" class="btn btn-success">
                                    <i class="bi bi-file-earmark-excel"></i>
                                    تصدير Excel
                                </a>
                                <a href="{{ url_for('export_pdf') }}" class="btn btn-danger">
                                    <i class="bi bi-file-earmark-pdf"></i>
                                    تصدير PDF
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table"></i>
                        قائمة التقارير
                    </h5>
                    <span class="badge bg-primary">
                        إجمالي: {{ reports.total }} تقرير
                    </span>
                </div>
                <div class="card-body">
                    {% if reports.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المريض</th>
                                    <th>رقم المريض</th>
                                    <th>نوع التقرير</th>
                                    <th>المرض</th>
                                    <th>نوع الملف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports.items %}
                                <tr>
                                    <td>
                                        <strong>{{ report.patient_name }}</strong>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ report.patient_id or '-' }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if report.report_type == 'ذوي الإعاقة' else 'info' }}">
                                            {{ report.report_type }}
                                        </span>
                                    </td>
                                    <td>{{ report.disease.name if report.disease else '-' }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ report.file_type }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ report.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" onclick="viewReport({{ report.id }})" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" onclick="downloadReport({{ report.id }})" title="تحميل">
                                                <i class="bi bi-download"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="editReport({{ report.id }})" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteReport({{ report.id }})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if reports.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if reports.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports_page', page=reports.prev_num) }}">السابق</a>
                            </li>
                            {% endif %}

                            {% for page_num in reports.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != reports.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('reports_page', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if reports.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports_page', page=reports.next_num) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark-x" style="font-size: 4rem; color: #6c757d;"></i>
                        <h4 class="mt-3 text-muted">لا توجد تقارير</h4>
                        <p class="text-muted">ابدأ بإضافة تقرير جديد باستخدام المسح الضوئي</p>
                        <a href="{{ url_for('scan_page') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            إضافة تقرير جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Report Details Modal -->
<div class="modal fade" id="reportDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-text"></i>
                    تفاصيل التقرير
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reportDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printReport()">
                    <i class="bi bi-printer"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Report Modal -->
<div class="modal fade" id="editReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil"></i>
                    تعديل التقرير
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editReportForm">
                    <input type="hidden" id="editReportId">

                    <div class="mb-3">
                        <label for="editPatientName" class="form-label">اسم المريض</label>
                        <input type="text" class="form-control" id="editPatientName" required>
                    </div>

                    <div class="mb-3">
                        <label for="editPatientId" class="form-label">رقم المريض</label>
                        <input type="text" class="form-control" id="editPatientId">
                    </div>

                    <div class="mb-3">
                        <label for="editNotes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="editNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveReportChanges()">
                    <i class="bi bi-check"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function applyFilters() {
    const searchPatient = document.getElementById('searchPatient').value;
    const reportType = document.getElementById('filterReportType').value;
    const dateFrom = document.getElementById('filterDateFrom').value;
    const dateTo = document.getElementById('filterDateTo').value;

    // بناء URL مع المعاملات
    const params = new URLSearchParams();
    if (searchPatient) params.append('patient', searchPatient);
    if (reportType) params.append('type', reportType);
    if (dateFrom) params.append('from', dateFrom);
    if (dateTo) params.append('to', dateTo);

    // إعادة توجيه مع الفلاتر
    window.location.href = '{{ url_for("reports_page") }}?' + params.toString();
}

function clearFilters() {
    document.getElementById('filterForm').reset();
    window.location.href = '{{ url_for("reports_page") }}';
}

function viewReport(reportId) {
    // محاكاة تحميل تفاصيل التقرير
    const modal = new bootstrap.Modal(document.getElementById('reportDetailsModal'));
    const content = document.getElementById('reportDetailsContent');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    // محاكاة تحميل البيانات
    setTimeout(() => {
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>بيانات المريض:</h6>
                    <p><strong>الاسم:</strong> مريض تجريبي</p>
                    <p><strong>الرقم:</strong> 12345</p>
                </div>
                <div class="col-md-6">
                    <h6>بيانات التقرير:</h6>
                    <p><strong>النوع:</strong> ذوي الإعاقة</p>
                    <p><strong>المرض:</strong> الشلل الدماغي</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="text-muted">معاينة الملف ستكون متاحة هنا</p>
                <div class="border rounded p-4 bg-light">
                    <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: #dc3545;"></i>
                    <p class="mt-2">ملف PDF</p>
                </div>
            </div>
        `;
    }, 1000);
}

function downloadReport(reportId) {
    showAlert('ميزة التحميل ستكون متاحة قريباً', 'info');
}

function editReport(reportId) {
    const modal = new bootstrap.Modal(document.getElementById('editReportModal'));

    // تحميل بيانات التقرير (محاكاة)
    document.getElementById('editReportId').value = reportId;
    document.getElementById('editPatientName').value = 'مريض تجريبي';
    document.getElementById('editPatientId').value = '12345';
    document.getElementById('editNotes').value = 'ملاحظات تجريبية';

    modal.show();
}

function saveReportChanges() {
    const reportId = document.getElementById('editReportId').value;
    const patientName = document.getElementById('editPatientName').value;
    const patientId = document.getElementById('editPatientId').value;
    const notes = document.getElementById('editNotes').value;

    // محاكاة حفظ التغييرات
    showLoading();

    setTimeout(() => {
        hideLoading();
        const modal = bootstrap.Modal.getInstance(document.getElementById('editReportModal'));
        modal.hide();
        showAlert('تم حفظ التغييرات بنجاح!', 'success');
    }, 2000);
}

function deleteReport(reportId) {
    if (confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
        showLoading();

        setTimeout(() => {
            hideLoading();
            showAlert('تم حذف التقرير بنجاح!', 'success');
            // إعادة تحميل الصفحة
            setTimeout(() => {
                location.reload();
            }, 1500);
        }, 2000);
    }
}

function printReport() {
    window.print();
}
</script>
{% endblock %}