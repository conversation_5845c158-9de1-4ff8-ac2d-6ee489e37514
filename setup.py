#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام أرشفة التقارير الطبية
Medical Reports Archive System

ملف الإعداد والتثبيت
Setup and Installation Script
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("📦 جاري تثبيت المكتبات المطلوبة...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت جميع المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ خطأ في تثبيت المكتبات")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "archives",
        "static/uploads",
        "backups"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 تم إنشاء مجلد: {directory}")
        else:
            print(f"📁 المجلد موجود: {directory}")

def check_scanner_support():
    """التحقق من دعم الماسحات الضوئية"""
    system = platform.system()
    print(f"🖥️  نظام التشغيل: {system}")
    
    if system == "Windows":
        try:
            import win32com.client
            print("✅ دعم الماسحات الضوئية لـ Windows متوفر")
        except ImportError:
            print("⚠️  تحذير: مكتبة win32com غير متوفرة")
            print("   للحصول على دعم كامل للماسحات الضوئية، قم بتثبيت:")
            print("   pip install pywin32")
    
    elif system == "Linux":
        try:
            import pyinsane2
            print("✅ دعم الماسحات الضوئية لـ Linux متوفر")
        except ImportError:
            print("⚠️  تحذير: مكتبة pyinsane2 غير متوفرة")
            print("   للحصول على دعم كامل للماسحات الضوئية، قم بتثبيت:")
            print("   pip install pyinsane2")
    
    else:
        print("⚠️  نظام التشغيل غير مدعوم بالكامل للمسح الضوئي")

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config_content = """# إعدادات نظام أرشفة التقارير الطبية
# Medical Reports Archive System Configuration

# إعدادات قاعدة البيانات
DATABASE_URL = 'sqlite:///medical_reports.db'

# إعدادات الأمان
SECRET_KEY = 'your-secret-key-change-this-in-production'

# إعدادات الملفات
UPLOAD_FOLDER = 'archives'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# إعدادات المسح الضوئي
DEFAULT_SCAN_QUALITY = '300dpi'
DEFAULT_FILE_TYPE = 'PDF'
SUPPORTED_FORMATS = ['PDF', 'JPG', 'PNG']

# إعدادات الخادم
HOST = '0.0.0.0'
PORT = 5000
DEBUG = True
"""
    
    if not os.path.exists('config.py'):
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("⚙️  تم إنشاء ملف الإعدادات: config.py")
    else:
        print("⚙️  ملف الإعدادات موجود: config.py")

def run_initial_setup():
    """تشغيل الإعداد الأولي"""
    print("🔧 جاري تشغيل الإعداد الأولي...")
    try:
        from database import init_database
        from app import app
        
        with app.app_context():
            init_database(app)
        
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 50)
    print("🏥 نظام أرشفة التقارير الطبية")
    print("   Medical Reports Archive System")
    print("=" * 50)
    print()
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    print()
    
    # تثبيت المكتبات
    if not install_requirements():
        sys.exit(1)
    
    print()
    
    # إنشاء المجلدات
    create_directories()
    
    print()
    
    # التحقق من دعم الماسحات الضوئية
    check_scanner_support()
    
    print()
    
    # إنشاء ملف الإعدادات
    create_config_file()
    
    print()
    
    # تشغيل الإعداد الأولي
    if not run_initial_setup():
        print("⚠️  تحذير: فشل في الإعداد الأولي، لكن يمكن المتابعة")
    
    print()
    print("🎉 تم الإعداد بنجاح!")
    print()
    print("📋 خطوات التشغيل:")
    print("   1. تشغيل البرنامج: python app.py")
    print("   2. فتح المتصفح: http://localhost:5000")
    print()
    print("📚 للمساعدة: راجع ملف README.md")
    print("=" * 50)

if __name__ == "__main__":
    main()
