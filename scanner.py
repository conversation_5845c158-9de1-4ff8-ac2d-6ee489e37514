import os
import platform
from PIL import Image, ImageEnhance
from datetime import datetime
import tempfile

class DocumentScanner:
    """فئة المسح الضوئي للمستندات"""
    
    def __init__(self):
        self.supported_formats = ['PDF', 'JPG', 'PNG']
        self.quality_settings = {
            '150dpi': 150,
            '300dpi': 300,
            '600dpi': 600
        }
    
    def get_available_scanners(self):
        """الحصول على قائمة الماسحات الضوئية المتاحة"""
        try:
            if platform.system() == "Windows":
                return self._get_windows_scanners()
            else:
                return self._get_linux_scanners()
        except Exception as e:
            print(f"خطأ في الحصول على الماسحات الضوئية: {e}")
            return []
    
    def _get_windows_scanners(self):
        """الحصول على الماسحات الضوئية في Windows"""
        try:
            import win32com.client
            wia = win32com.client.Dispatch("WIA.DeviceManager")
            devices = []
            for device in wia.DeviceInfos:
                if device.Type == 1:  # Scanner type
                    devices.append({
                        'id': device.DeviceID,
                        'name': device.Properties("Name").Value
                    })
            return devices
        except ImportError:
            print("مكتبة win32com غير متوفرة")
            return []
        except Exception as e:
            print(f"خطأ في Windows scanner detection: {e}")
            return []
    
    def _get_linux_scanners(self):
        """الحصول على الماسحات الضوئية في Linux"""
        try:
            import pyinsane2
            pyinsane2.init()
            devices = pyinsane2.get_devices()
            scanner_list = []
            for device in devices:
                scanner_list.append({
                    'id': device.name,
                    'name': device.nice_name
                })
            return scanner_list
        except ImportError:
            print("مكتبة pyinsane2 غير متوفرة")
            return []
        except Exception as e:
            print(f"خطأ في Linux scanner detection: {e}")
            return []
    
    def scan_document(self, scanner_id=None, quality='300dpi', file_format='PDF'):
        """مسح مستند ضوئياً"""
        try:
            if platform.system() == "Windows":
                return self._scan_windows(scanner_id, quality, file_format)
            else:
                return self._scan_linux(scanner_id, quality, file_format)
        except Exception as e:
            print(f"خطأ في المسح الضوئي: {e}")
            return None
    
    def _scan_windows(self, scanner_id, quality, file_format):
        """المسح الضوئي في Windows"""
        try:
            import win32com.client
            
            # إنشاء كائن WIA
            wia = win32com.client.Dispatch("WIA.DeviceManager")
            
            # اختيار الماسح الضوئي
            if scanner_id:
                device = None
                for dev in wia.DeviceInfos:
                    if dev.DeviceID == scanner_id:
                        device = dev.Connect()
                        break
            else:
                # استخدام أول ماسح ضوئي متاح
                device = wia.DeviceInfos(1).Connect()
            
            if not device:
                raise Exception("لم يتم العثور على ماسح ضوئي")
            
            # إعداد جودة المسح
            dpi = self.quality_settings.get(quality, 300)
            
            # إعداد خصائص المسح
            item = device.Items(1)
            item.Properties("Horizontal Resolution").Value = dpi
            item.Properties("Vertical Resolution").Value = dpi
            
            # تنفيذ المسح
            image = item.Transfer()
            
            # حفظ الصورة مؤقتاً
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bmp')
            image.SaveFile(temp_file.name)
            temp_file.close()
            
            # تحويل إلى التنسيق المطلوب
            return self._convert_image(temp_file.name, file_format)
            
        except Exception as e:
            print(f"خطأ في Windows scanning: {e}")
            return None
    
    def _scan_linux(self, scanner_id, quality, file_format):
        """المسح الضوئي في Linux"""
        try:
            import pyinsane2
            
            pyinsane2.init()
            devices = pyinsane2.get_devices()
            
            # اختيار الماسح الضوئي
            device = None
            if scanner_id:
                for dev in devices:
                    if dev.name == scanner_id:
                        device = dev
                        break
            else:
                device = devices[0] if devices else None
            
            if not device:
                raise Exception("لم يتم العثور على ماسح ضوئي")
            
            # إعداد جودة المسح
            dpi = self.quality_settings.get(quality, 300)
            device.options['resolution'].value = dpi
            
            # تنفيذ المسح
            scan_session = device.scan(multiple=False)
            
            try:
                while True:
                    scan_session.scan.read()
            except EOFError:
                pass
            
            # الحصول على الصورة
            image = scan_session.images[0]
            
            # حفظ الصورة مؤقتاً
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            image.save(temp_file.name)
            temp_file.close()
            
            # تحويل إلى التنسيق المطلوب
            return self._convert_image(temp_file.name, file_format)
            
        except Exception as e:
            print(f"خطأ في Linux scanning: {e}")
            return None
    
    def _convert_image(self, temp_path, target_format):
        """تحويل الصورة إلى التنسيق المطلوب"""
        try:
            # فتح الصورة
            image = Image.open(temp_path)
            
            # تحسين جودة الصورة
            image = self._enhance_image(image)
            
            # إنشاء ملف مؤقت للنتيجة
            if target_format.upper() == 'PDF':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
                image.save(output_file.name, 'PDF', resolution=100.0, save_all=True)
            elif target_format.upper() == 'JPG':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                if image.mode == 'RGBA':
                    image = image.convert('RGB')
                image.save(output_file.name, 'JPEG', quality=95)
            elif target_format.upper() == 'PNG':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                image.save(output_file.name, 'PNG')
            else:
                raise Exception(f"تنسيق غير مدعوم: {target_format}")
            
            output_file.close()
            
            # حذف الملف المؤقت الأصلي
            try:
                os.unlink(temp_path)
            except:
                pass
            
            return output_file.name
            
        except Exception as e:
            print(f"خطأ في تحويل الصورة: {e}")
            return None
    
    def _enhance_image(self, image):
        """تحسين جودة الصورة"""
        try:
            # تحسين التباين
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            # تحسين الحدة
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # تحسين السطوع
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.05)
            
            return image
        except Exception as e:
            print(f"خطأ في تحسين الصورة: {e}")
            return image
    
    def simulate_scan(self, file_format='PDF'):
        """محاكاة المسح الضوئي لأغراض التطوير والاختبار"""
        try:
            # إنشاء صورة وهمية
            image = Image.new('RGB', (2480, 3508), color='white')  # A4 size at 300 DPI
            
            # إضافة نص وهمي
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(image)
            
            try:
                # محاولة استخدام خط عربي
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()
            
            text = f"تقرير طبي وهمي\nتاريخ المسح: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            draw.text((100, 100), text, fill='black', font=font)
            
            # حفظ الصورة
            if file_format.upper() == 'PDF':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
                image.save(output_file.name, 'PDF')
            elif file_format.upper() == 'JPG':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                image.save(output_file.name, 'JPEG', quality=95)
            elif file_format.upper() == 'PNG':
                output_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                image.save(output_file.name, 'PNG')
            
            output_file.close()
            return output_file.name
            
        except Exception as e:
            print(f"خطأ في محاكاة المسح: {e}")
            return None
