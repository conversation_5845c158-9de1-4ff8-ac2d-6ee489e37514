// ملف JavaScript المشترك
// Common JavaScript Functions

// دالة محسنة لعرض نافذة التحميل
function showLoading() {
    try {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            const modal = new bootstrap.Modal(loadingModal, {
                backdrop: 'static',
                keyboard: false
            });
            modal.show();
        }
    } catch (error) {
        console.log('خطأ في عرض نافذة التحميل:', error);
    }
}

// دالة محسنة لإخفاء نافذة التحميل
function hideLoading() {
    try {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            const modalInstance = bootstrap.Modal.getInstance(loadingModal);
            if (modalInstance) {
                modalInstance.hide();
            }
            
            // إزالة backdrop يدوياً إذا كان موجوداً
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            // إزالة فئة modal-open من body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    } catch (error) {
        console.log('خطأ في إخفاء نافذة التحميل:', error);
    }
}

// دالة محسنة لعرض التنبيهات
function showAlert(message, type = 'info') {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert.auto-dismiss');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show auto-dismiss`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// دالة تنسيق التواريخ
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

// دالة تنسيق الأرقام
function formatNumber(number) {
    try {
        return new Intl.NumberFormat('ar-SA').format(number);
    } catch (error) {
        return number.toString();
    }
}

// دالة للتحقق من حالة الشبكة
function checkNetworkStatus() {
    return navigator.onLine;
}

// دالة لإعادة المحاولة مع تأخير
function retryWithDelay(fn, delay = 1000, maxRetries = 3) {
    let retries = 0;
    
    function attempt() {
        return fn().catch(error => {
            if (retries < maxRetries) {
                retries++;
                console.log(`إعادة المحاولة ${retries}/${maxRetries} بعد ${delay}ms`);
                return new Promise(resolve => {
                    setTimeout(() => resolve(attempt()), delay);
                });
            } else {
                throw error;
            }
        });
    }
    
    return attempt();
}

// دالة لتنظيف النماذج
function clearForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        
        // إزالة رسائل الخطأ
        const errorMessages = form.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(msg => msg.remove());
        
        // إزالة فئات التحقق
        const inputs = form.querySelectorAll('.form-control, .form-select');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
    }
}

// دالة للتحقق من صحة النماذج
function validateForm(formId, rules) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    let isValid = true;
    
    Object.keys(rules).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        const rule = rules[fieldName];
        
        if (field) {
            let fieldValid = true;
            let errorMessage = '';
            
            // التحقق من الحقول المطلوبة
            if (rule.required && !field.value.trim()) {
                fieldValid = false;
                errorMessage = rule.requiredMessage || 'هذا الحقل مطلوب';
            }
            
            // التحقق من الحد الأدنى للطول
            if (fieldValid && rule.minLength && field.value.length < rule.minLength) {
                fieldValid = false;
                errorMessage = rule.minLengthMessage || `يجب أن يكون الطول ${rule.minLength} أحرف على الأقل`;
            }
            
            // التحقق من النمط
            if (fieldValid && rule.pattern && !rule.pattern.test(field.value)) {
                fieldValid = false;
                errorMessage = rule.patternMessage || 'تنسيق غير صحيح';
            }
            
            // تطبيق النتيجة
            if (fieldValid) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
                
                // إزالة رسالة الخطأ السابقة
                const existingError = field.parentNode.querySelector('.invalid-feedback');
                if (existingError) {
                    existingError.remove();
                }
            } else {
                field.classList.remove('is-valid');
                field.classList.add('is-invalid');
                
                // إضافة رسالة الخطأ
                let errorDiv = field.parentNode.querySelector('.invalid-feedback');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    field.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = errorMessage;
                
                isValid = false;
            }
        }
    });
    
    return isValid;
}

// دالة لحفظ البيانات في التخزين المحلي
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        return false;
    }
}

// دالة لاسترجاع البيانات من التخزين المحلي
function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
        console.error('خطأ في استرجاع البيانات:', error);
        return defaultValue;
    }
}

// دالة لتصدير البيانات إلى CSV
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// دالة لطباعة عنصر معين
function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (!element) {
        showAlert('العنصر المطلوب طباعته غير موجود', 'error');
        return;
    }
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>طباعة</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    @media print { .no-print { display: none !important; } }
                </style>
            </head>
            <body>
                ${element.innerHTML}
            </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 250);
}

// تهيئة الدوال المشتركة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء نافذة التحميل عند تحميل الصفحة
    hideLoading();
    
    // إضافة مستمع لحالة الشبكة
    window.addEventListener('online', function() {
        showAlert('تم استعادة الاتصال بالإنترنت', 'success');
    });
    
    window.addEventListener('offline', function() {
        showAlert('انقطع الاتصال بالإنترنت', 'warning');
    });
    
    // إضافة تأثيرات التحميل للنماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                setTimeout(() => {
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    });
});
