#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام السجلات لبرنامج أرشفة التقارير الطبية
Logging System for Medical Reports Archive
"""

import logging
import logging.handlers
import os
from datetime import datetime
import config

class MedicalReportsLogger:
    """فئة إدارة السجلات"""
    
    def __init__(self, name='MedicalReports'):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """إعداد نظام السجلات"""
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        log_dir = os.path.dirname(config.LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # تعيين مستوى السجل
        level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # منع التكرار
        if self.logger.handlers:
            return
        
        # إعداد تنسيق السجل
        formatter = logging.Formatter(
            config.LOG_FORMAT,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # معالج الملف مع التدوير
        file_handler = logging.handlers.RotatingFileHandler(
            config.LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.WARNING)
        
        # إضافة المعالجات
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message, **kwargs):
        """تسجيل رسالة معلومات"""
        self.logger.info(self._format_message(message, **kwargs))
    
    def warning(self, message, **kwargs):
        """تسجيل رسالة تحذير"""
        self.logger.warning(self._format_message(message, **kwargs))
    
    def error(self, message, **kwargs):
        """تسجيل رسالة خطأ"""
        self.logger.error(self._format_message(message, **kwargs))
    
    def debug(self, message, **kwargs):
        """تسجيل رسالة تصحيح"""
        self.logger.debug(self._format_message(message, **kwargs))
    
    def critical(self, message, **kwargs):
        """تسجيل رسالة حرجة"""
        self.logger.critical(self._format_message(message, **kwargs))
    
    def _format_message(self, message, **kwargs):
        """تنسيق الرسالة مع معلومات إضافية"""
        if kwargs:
            extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            return f"{message} | {extra_info}"
        return message
    
    def log_user_action(self, action, user_id=None, details=None):
        """تسجيل إجراء المستخدم"""
        message = f"إجراء المستخدم: {action}"
        kwargs = {}
        if user_id:
            kwargs['user_id'] = user_id
        if details:
            kwargs['details'] = details
        self.info(message, **kwargs)
    
    def log_scan_operation(self, patient_name, file_type, quality, success=True):
        """تسجيل عملية المسح الضوئي"""
        status = "نجح" if success else "فشل"
        message = f"مسح ضوئي {status}"
        self.info(message, 
                 patient=patient_name, 
                 file_type=file_type, 
                 quality=quality)
    
    def log_database_operation(self, operation, table, record_id=None, success=True):
        """تسجيل عملية قاعدة البيانات"""
        status = "نجح" if success else "فشل"
        message = f"عملية قاعدة البيانات {status}: {operation}"
        kwargs = {'table': table}
        if record_id:
            kwargs['record_id'] = record_id
        self.info(message, **kwargs)
    
    def log_export_operation(self, export_type, record_count, success=True):
        """تسجيل عملية التصدير"""
        status = "نجح" if success else "فشل"
        message = f"تصدير {status}"
        self.info(message, 
                 export_type=export_type, 
                 record_count=record_count)
    
    def log_system_event(self, event, details=None):
        """تسجيل حدث النظام"""
        message = f"حدث النظام: {event}"
        kwargs = {}
        if details:
            kwargs['details'] = details
        self.info(message, **kwargs)
    
    def log_error_with_traceback(self, message, exception):
        """تسجيل خطأ مع تفاصيل التتبع"""
        import traceback
        error_details = traceback.format_exc()
        self.error(f"{message}: {str(exception)}", traceback=error_details)

# إنشاء مثيل عام للسجل
logger = MedicalReportsLogger()

# دوال مساعدة للاستخدام السريع
def log_info(message, **kwargs):
    """تسجيل معلومات"""
    logger.info(message, **kwargs)

def log_warning(message, **kwargs):
    """تسجيل تحذير"""
    logger.warning(message, **kwargs)

def log_error(message, **kwargs):
    """تسجيل خطأ"""
    logger.error(message, **kwargs)

def log_debug(message, **kwargs):
    """تسجيل تصحيح"""
    logger.debug(message, **kwargs)

def log_user_action(action, user_id=None, details=None):
    """تسجيل إجراء المستخدم"""
    logger.log_user_action(action, user_id, details)

def log_scan_operation(patient_name, file_type, quality, success=True):
    """تسجيل عملية المسح الضوئي"""
    logger.log_scan_operation(patient_name, file_type, quality, success)

def log_database_operation(operation, table, record_id=None, success=True):
    """تسجيل عملية قاعدة البيانات"""
    logger.log_database_operation(operation, table, record_id, success)

def log_export_operation(export_type, record_count, success=True):
    """تسجيل عملية التصدير"""
    logger.log_export_operation(export_type, record_count, success)

def log_system_event(event, details=None):
    """تسجيل حدث النظام"""
    logger.log_system_event(event, details)

def log_error_with_traceback(message, exception):
    """تسجيل خطأ مع تفاصيل التتبع"""
    logger.log_error_with_traceback(message, exception)

# ديكوريتر لتسجيل استدعاء الدوال
def log_function_call(func):
    """ديكوريتر لتسجيل استدعاء الدوال"""
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        log_debug(f"استدعاء الدالة: {func_name}", args=str(args), kwargs=str(kwargs))
        try:
            result = func(*args, **kwargs)
            log_debug(f"انتهاء الدالة: {func_name}", result="نجح")
            return result
        except Exception as e:
            log_error_with_traceback(f"خطأ في الدالة: {func_name}", e)
            raise
    return wrapper

# دالة لتنظيف السجلات القديمة
def cleanup_old_logs(days=30):
    """حذف السجلات الأقدم من عدد الأيام المحدد"""
    import glob
    from datetime import timedelta
    
    log_dir = os.path.dirname(config.LOG_FILE)
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # البحث عن ملفات السجل القديمة
    log_pattern = os.path.join(log_dir, "*.log*")
    log_files = glob.glob(log_pattern)
    
    deleted_count = 0
    for log_file in log_files:
        try:
            file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
            if file_time < cutoff_date:
                os.remove(log_file)
                deleted_count += 1
        except Exception as e:
            log_error(f"خطأ في حذف ملف السجل {log_file}", error=str(e))
    
    if deleted_count > 0:
        log_info(f"تم حذف {deleted_count} ملف سجل قديم")

# دالة لإنشاء تقرير السجلات
def generate_log_report(days=7):
    """إنشاء تقرير عن السجلات لعدد الأيام المحدد"""
    import re
    from collections import defaultdict
    
    log_file = config.LOG_FILE
    if not os.path.exists(log_file):
        return None
    
    stats = defaultdict(int)
    errors = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # تحليل مستوى السجل
                if ' - INFO - ' in line:
                    stats['info'] += 1
                elif ' - WARNING - ' in line:
                    stats['warning'] += 1
                elif ' - ERROR - ' in line:
                    stats['error'] += 1
                    errors.append(line.strip())
                elif ' - DEBUG - ' in line:
                    stats['debug'] += 1
                elif ' - CRITICAL - ' in line:
                    stats['critical'] += 1
    
    except Exception as e:
        log_error(f"خطأ في قراءة ملف السجل", error=str(e))
        return None
    
    return {
        'stats': dict(stats),
        'recent_errors': errors[-10:],  # آخر 10 أخطاء
        'total_entries': sum(stats.values())
    }
